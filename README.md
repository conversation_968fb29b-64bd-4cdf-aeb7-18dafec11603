# Pharmaceutical Data Scraper

A comprehensive Python web scraping application that extracts drug information from three major pharmaceutical websites: 1mg.com, Apollo Pharmacy, and IndiaMART. The application provides unified data extraction, standardization, and Excel export functionality.

## Features

- **Multi-source scraping**: Extracts data from 1mg.com, Apollo Pharmacy, and IndiaMART
- **Comprehensive data extraction**: Drug name, manufacturer, composition, pricing, availability, therapeutic class, side effects, and more
- **Unified data model**: Standardized schema across all sources with source identification
- **Respectful scraping**: Rate limiting, error handling, and robots.txt compliance
- **Excel export**: Professional Excel output with formatting, metadata, and statistics
- **Data standardization**: Automatic cleaning and normalization of extracted data
- **CLI interface**: Easy-to-use command-line interface
- **Comprehensive logging**: Detailed logging and progress tracking

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure settings** (optional):
   Edit `config.yaml` to customize scraping parameters, rate limits, and output settings.

## Quick Start

### Basic Usage

```bash
# Scrape paracetamol data from all sources
python -m src.main paracetamol

# Scrape multiple drugs from specific sources
python -m src.main paracetamol aspirin ibuprofen --sources 1mg apollo

# Limit results and specify output file
python -m src.main paracetamol --limit 20 --output my_drugs.xlsx
```

### Python API Usage

```python
from src.main import PharmaceuticalScraper

# Initialize scraper
scraper = PharmaceuticalScraper('config.yaml')

# Scrape data
drugs = scraper.scrape_drugs(
    queries=['paracetamol', 'aspirin'],
    sources=['1mg', 'apollo'],
    limit_per_query=25
)

# Export to Excel
output_file = scraper.export_data(drugs, 'pharmaceutical_data.xlsx')
print(f"Data exported to: {output_file}")
```

## Data Schema

The application extracts the following information for each drug:

### Core Information
- **source_website**: Origin website (1mg, Apollo Pharmacy, IndiaMART)
- **drug_name**: Standardized drug name
- **brand_name**: Brand/trade name
- **generic_name**: Generic name
- **manufacturer**: Manufacturing company
- **composition**: Active ingredients

### Product Details
- **dosage_form**: Tablet, capsule, syrup, etc.
- **strength**: Dosage strength (mg, ml, etc.)
- **pack_size**: Number of units in pack

### Pricing
- **mrp**: Maximum Retail Price
- **selling_price**: Current selling price
- **discount_percentage**: Calculated discount
- **price_per_unit**: Price per individual unit
- **currency**: Currency (default: INR)

### Availability
- **availability_status**: In Stock, Out of Stock, Limited Stock
- **stock_quantity**: Available quantity (if provided)

### Medical Information
- **therapeutic_class**: Drug classification
- **drug_category**: Prescription, OTC, etc.
- **uses_indications**: Medical uses
- **side_effects**: Known side effects
- **contraindications**: Contraindications
- **dosage_instructions**: Usage instructions

### Additional Data
- **product_url**: Source product page URL
- **image_urls**: Product image URLs
- **product_id**: Website-specific product ID
- **seller_information**: Seller details (especially for IndiaMART)

## Command Line Options

```bash
python -m src.main [queries] [options]

Positional arguments:
  queries              Search queries for drugs (space-separated)

Optional arguments:
  --sources {1mg,apollo,indiamart}
                      Sources to scrape from (default: all)
  --limit LIMIT       Maximum results per query per source (default: 50)
  --output OUTPUT     Output Excel filename
  --config CONFIG     Configuration file path (default: config.yaml)
  --verbose           Enable verbose logging
```

## Configuration

The `config.yaml` file allows customization of:

- **Rate limiting**: Delays between requests
- **Retry settings**: Maximum retries and timeouts
- **Website URLs**: Base and search URLs for each site
- **Output settings**: Excel formatting and metadata options
- **Logging**: Log levels and formats

## Output Files

The application generates Excel files with multiple sheets:

1. **Drug_Data**: Main dataset with all extracted information
2. **Metadata**: Scraping session information and parameters
3. **Statistics**: Data quality metrics and summary statistics
4. **Source_Summary**: Source-wise data coverage and statistics

## Examples

### Example 1: Basic Drug Search
```bash
python -m src.main paracetamol
```

### Example 2: Multiple Drugs, Specific Sources
```bash
python -m src.main paracetamol aspirin ibuprofen --sources 1mg apollo --limit 30
```

### Example 3: Custom Output File
```bash
python -m src.main "vitamin d" "vitamin c" --output vitamins_data.xlsx
```

### Example 4: Verbose Logging
```bash
python -m src.main antibiotics --verbose
```

## Data Quality Features

- **Automatic deduplication**: Removes duplicate entries
- **Data standardization**: Normalizes drug names, dosage forms, and prices
- **Validation**: Checks for data consistency and validity
- **Error handling**: Graceful handling of missing or malformed data
- **Source tracking**: Maintains data lineage for each record

## Ethical Considerations

This scraper is designed with ethical web scraping practices:

- **Rate limiting**: Respectful delays between requests
- **User agent rotation**: Mimics normal browser behavior
- **Error handling**: Graceful failure without overwhelming servers
- **Robots.txt compliance**: Respects website scraping policies
- **Educational purpose**: Intended for research and educational use

## Troubleshooting

### Common Issues

1. **No data scraped**: Check internet connection and website availability
2. **Rate limiting errors**: Increase delays in config.yaml
3. **Import errors**: Ensure all dependencies are installed
4. **Excel export errors**: Check file permissions and disk space

### Debugging

Enable verbose logging for detailed information:
```bash
python -m src.main paracetamol --verbose
```

Check the log file (`scraper.log`) for detailed error information.

## Project Structure

```
pharmaceutical-scraper/
├── src/
│   ├── __init__.py
│   ├── main.py                 # Main application and CLI
│   ├── models/
│   │   ├── __init__.py
│   │   └── drug_data.py        # Data models
│   ├── scrapers/
│   │   ├── __init__.py
│   │   ├── base_scraper.py     # Base scraper class
│   │   ├── onemg_scraper.py    # 1mg.com scraper
│   │   ├── apollo_scraper.py   # Apollo Pharmacy scraper
│   │   └── indiamart_scraper.py # IndiaMART scraper
│   └── utils/
│       ├── __init__.py
│       ├── data_processor.py   # Data processing utilities
│       └── excel_exporter.py   # Excel export functionality
├── config.yaml                 # Configuration file
├── requirements.txt            # Python dependencies
└── README.md                   # This file
```

## License

This project is for educational and research purposes. Please respect the terms of service of the websites being scraped and use responsibly.

## Contributing

Contributions are welcome! Please ensure that any modifications maintain the ethical scraping practices and data quality standards established in this project.
