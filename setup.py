#!/usr/bin/env python3
"""
Setup script for the Pharmaceutical Data Scraper.
Helps users install dependencies and verify the installation.
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing dependencies: {e}")
        return False

def verify_installation():
    """Verify that the installation is working."""
    print("\nVerifying installation...")
    
    try:
        # Test imports
        from src.models.drug_data import DrugInformation, SourceWebsite
        from src.scrapers.base_scraper import BaseScraper
        from src.utils.data_processor import DataProcessor
        from src.utils.excel_exporter import ExcelExporter
        from src.main import PharmaceuticalScraper
        
        print("✓ All modules imported successfully!")
        
        # Test basic functionality
        processor = DataProcessor()
        test_name = processor.standardize_drug_name("paracetamol tablet")
        if test_name:
            print("✓ Data processor working!")
        
        # Test drug model
        drug = DrugInformation(
            source_website=SourceWebsite.ONEMG,
            drug_name="Test Drug"
        )
        if drug.to_dict():
            print("✓ Drug data model working!")
        
        print("✓ Installation verified successfully!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Verification error: {e}")
        return False

def run_basic_test():
    """Run basic tests to ensure everything works."""
    print("\nRunning basic tests...")
    
    try:
        # Run the test suite
        subprocess.check_call([sys.executable, '-m', 'pytest', 'tests/', '-v'])
        print("✓ All tests passed!")
        return True
    except subprocess.CalledProcessError:
        print("✗ Some tests failed. Check the output above.")
        return False
    except FileNotFoundError:
        print("ℹ pytest not found. Running basic verification instead...")
        try:
            subprocess.check_call([sys.executable, 'tests/test_basic.py'])
            print("✓ Basic tests passed!")
            return True
        except subprocess.CalledProcessError:
            print("✗ Basic tests failed.")
            return False

def create_sample_config():
    """Create a sample configuration file if it doesn't exist."""
    config_path = Path('config.yaml')
    
    if config_path.exists():
        print("✓ Configuration file already exists!")
        return True
    
    print("Creating sample configuration file...")
    
    sample_config = """# Pharmaceutical Data Scraper Configuration

# Scraping settings
scraping:
  delay_between_requests: 2  # seconds
  max_retries: 3
  timeout: 30  # seconds
  concurrent_requests: 1  # Keep low to be respectful
  
# User agents for rotation
user_agents:
  - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# Website-specific configurations
websites:
  onemg:
    base_url: "https://www.1mg.com"
    search_url: "https://www.1mg.com/search/all"
    rate_limit: 2  # seconds between requests
    
  apollo:
    base_url: "https://www.apollopharmacy.in"
    search_url: "https://www.apollopharmacy.in/search-medicines"
    rate_limit: 2
    
  indiamart:
    base_url: "https://www.indiamart.com"
    search_url: "https://www.indiamart.com/search.mp"
    rate_limit: 3  # More conservative for IndiaMART

# Output settings
output:
  excel_filename: "pharmaceutical_data_{timestamp}.xlsx"
  include_metadata: true
  max_records_per_sheet: 1000000  # Excel limit
  
# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "scraper.log"
"""
    
    try:
        with open(config_path, 'w') as f:
            f.write(sample_config)
        print("✓ Sample configuration file created!")
        return True
    except Exception as e:
        print(f"✗ Error creating configuration file: {e}")
        return False

def main():
    """Main setup function."""
    print("=" * 60)
    print("PHARMACEUTICAL DATA SCRAPER - SETUP")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("✗ Python 3.7 or higher is required!")
        sys.exit(1)
    
    print(f"✓ Python {sys.version.split()[0]} detected")
    
    # Install dependencies
    if not install_dependencies():
        print("\nSetup failed during dependency installation.")
        sys.exit(1)
    
    # Create sample config
    create_sample_config()
    
    # Verify installation
    if not verify_installation():
        print("\nSetup failed during verification.")
        sys.exit(1)
    
    # Run tests
    print("\nWould you like to run basic tests? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes']:
            run_basic_test()
    except KeyboardInterrupt:
        print("\nSkipping tests...")
    
    print("\n" + "=" * 60)
    print("SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nYou can now use the scraper:")
    print("1. Command line: python -m src.main paracetamol")
    print("2. Python script: python example_usage.py")
    print("3. Custom script: from src.main import PharmaceuticalScraper")
    print("\nFor more information, see README.md")

if __name__ == '__main__':
    main()
