"""
Excel export functionality for pharmaceutical data.
Provides comprehensive Excel export with formatting, metadata, and statistics.
"""

import pandas as pd
import logging
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

from ..models.drug_data import DrugInformation, ScrapingMetadata


class ExcelExporter:
    """
    Handles export of pharmaceutical data to Excel format with proper formatting.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Excel styling
        self.header_font = Font(bold=True, color="FFFFFF")
        self.header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        self.center_alignment = Alignment(horizontal='center', vertical='center')
    
    def export_to_excel(self, 
                       drugs: List[DrugInformation], 
                       metadata: ScrapingMetadata,
                       filename: str = None) -> str:
        """
        Export drug data to Excel file with multiple sheets.
        
        Args:
            drugs: List of DrugInformation objects
            metadata: Scraping metadata
            filename: Output filename (optional)
            
        Returns:
            Path to the created Excel file
        """
        try:
            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = self.config.get('output', {}).get('excel_filename', 
                    'pharmaceutical_data_{timestamp}.xlsx').format(timestamp=timestamp)
            
            # Ensure .xlsx extension
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            
            self.logger.info(f"Exporting {len(drugs)} records to Excel: {filename}")
            
            # Create Excel writer
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # Export main data
                self._export_main_data(drugs, writer)
                
                # Export metadata
                if self.config.get('output', {}).get('include_metadata', True):
                    self._export_metadata(metadata, writer)
                
                # Export statistics
                self._export_statistics(drugs, writer)
                
                # Export source-wise data
                self._export_source_wise_data(drugs, writer)
            
            # Apply formatting
            self._apply_formatting(filename)
            
            self.logger.info(f"Successfully exported data to: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error exporting to Excel: {str(e)}")
            raise
    
    def _export_main_data(self, drugs: List[DrugInformation], writer: pd.ExcelWriter):
        """Export main drug data to Excel sheet."""
        if not drugs:
            return
        
        # Convert to DataFrame
        data_rows = []
        for drug in drugs:
            data_rows.append(drug.to_dict())
        
        df = pd.DataFrame(data_rows, columns=DrugInformation.get_column_headers())
        
        # Split into multiple sheets if data is too large
        max_records = self.config.get('output', {}).get('max_records_per_sheet', 1000000)
        
        if len(df) <= max_records:
            df.to_excel(writer, sheet_name='Drug_Data', index=False)
        else:
            # Split into multiple sheets
            for i, start_idx in enumerate(range(0, len(df), max_records)):
                end_idx = min(start_idx + max_records, len(df))
                sheet_name = f'Drug_Data_{i+1}'
                df.iloc[start_idx:end_idx].to_excel(writer, sheet_name=sheet_name, index=False)
    
    def _export_metadata(self, metadata: ScrapingMetadata, writer: pd.ExcelWriter):
        """Export scraping metadata to Excel sheet."""
        metadata_dict = metadata.to_dict()
        
        # Create metadata DataFrame
        metadata_rows = []
        for key, value in metadata_dict.items():
            if key == 'records_by_source':
                for source, count in value.items():
                    metadata_rows.append({'Metric': f'Records from {source}', 'Value': count})
            elif key == 'scraping_parameters':
                for param, param_value in value.items():
                    metadata_rows.append({'Metric': f'Parameter: {param}', 'Value': str(param_value)})
            else:
                metadata_rows.append({'Metric': key.replace('_', ' ').title(), 'Value': str(value)})
        
        metadata_df = pd.DataFrame(metadata_rows)
        metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
    
    def _export_statistics(self, drugs: List[DrugInformation], writer: pd.ExcelWriter):
        """Export data statistics to Excel sheet."""
        stats = self._calculate_statistics(drugs)
        
        # Create statistics DataFrame
        stats_rows = []
        for category, metrics in stats.items():
            stats_rows.append({'Category': category.title(), 'Metric': '', 'Value': ''})
            for metric, value in metrics.items():
                stats_rows.append({'Category': '', 'Metric': metric, 'Value': str(value)})
            stats_rows.append({'Category': '', 'Metric': '', 'Value': ''})  # Empty row
        
        stats_df = pd.DataFrame(stats_rows)
        stats_df.to_excel(writer, sheet_name='Statistics', index=False)
    
    def _export_source_wise_data(self, drugs: List[DrugInformation], writer: pd.ExcelWriter):
        """Export source-wise summary data."""
        source_data = {}
        
        for drug in drugs:
            source = drug.source_website.value
            if source not in source_data:
                source_data[source] = {
                    'total_records': 0,
                    'with_price': 0,
                    'with_composition': 0,
                    'with_manufacturer': 0,
                    'avg_price': 0,
                    'price_sum': 0,
                    'price_count': 0
                }
            
            source_data[source]['total_records'] += 1
            
            if drug.selling_price:
                source_data[source]['with_price'] += 1
                source_data[source]['price_sum'] += drug.selling_price
                source_data[source]['price_count'] += 1
            
            if drug.composition:
                source_data[source]['with_composition'] += 1
            
            if drug.manufacturer:
                source_data[source]['with_manufacturer'] += 1
        
        # Calculate averages
        for source in source_data:
            if source_data[source]['price_count'] > 0:
                source_data[source]['avg_price'] = source_data[source]['price_sum'] / source_data[source]['price_count']
        
        # Create DataFrame
        summary_rows = []
        for source, data in source_data.items():
            summary_rows.append({
                'Source': source,
                'Total Records': data['total_records'],
                'Records with Price': data['with_price'],
                'Records with Composition': data['with_composition'],
                'Records with Manufacturer': data['with_manufacturer'],
                'Average Price (INR)': round(data['avg_price'], 2) if data['avg_price'] > 0 else 'N/A',
                'Price Coverage (%)': round((data['with_price'] / data['total_records']) * 100, 1),
                'Composition Coverage (%)': round((data['with_composition'] / data['total_records']) * 100, 1),
                'Manufacturer Coverage (%)': round((data['with_manufacturer'] / data['total_records']) * 100, 1)
            })
        
        summary_df = pd.DataFrame(summary_rows)
        summary_df.to_excel(writer, sheet_name='Source_Summary', index=False)
    
    def _calculate_statistics(self, drugs: List[DrugInformation]) -> Dict[str, Dict[str, Any]]:
        """Calculate comprehensive statistics for the dataset."""
        stats = {
            'general': {
                'total_records': len(drugs),
                'unique_drugs': len(set(drug.drug_name for drug in drugs if drug.drug_name)),
                'unique_manufacturers': len(set(drug.manufacturer for drug in drugs if drug.manufacturer)),
                'records_with_price': sum(1 for drug in drugs if drug.selling_price),
                'records_with_composition': sum(1 for drug in drugs if drug.composition),
            },
            'pricing': {
                'min_price': min((drug.selling_price for drug in drugs if drug.selling_price), default=0),
                'max_price': max((drug.selling_price for drug in drugs if drug.selling_price), default=0),
                'avg_price': sum(drug.selling_price for drug in drugs if drug.selling_price) / max(1, sum(1 for drug in drugs if drug.selling_price)),
                'records_with_discount': sum(1 for drug in drugs if drug.discount_percentage and drug.discount_percentage > 0),
            },
            'sources': {}
        }
        
        # Source-wise statistics
        for drug in drugs:
            source = drug.source_website.value
            if source not in stats['sources']:
                stats['sources'][source] = 0
            stats['sources'][source] += 1
        
        # Round pricing values
        stats['pricing']['avg_price'] = round(stats['pricing']['avg_price'], 2)
        
        return stats
    
    def _apply_formatting(self, filename: str):
        """Apply formatting to the Excel file."""
        try:
            workbook = openpyxl.load_workbook(filename)
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # Format headers
                if worksheet.max_row > 0:
                    for cell in worksheet[1]:  # First row
                        cell.font = self.header_font
                        cell.fill = self.header_fill
                        cell.alignment = self.center_alignment
                        cell.border = self.border
                
                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
                # Add borders to all cells with data
                for row in worksheet.iter_rows(min_row=1, max_row=worksheet.max_row, 
                                             min_col=1, max_col=worksheet.max_column):
                    for cell in row:
                        cell.border = self.border
            
            workbook.save(filename)
            
        except Exception as e:
            self.logger.warning(f"Could not apply formatting to Excel file: {str(e)}")
    
    def export_summary_report(self, drugs: List[DrugInformation], filename: str = None) -> str:
        """
        Export a summary report with key insights.
        
        Args:
            drugs: List of DrugInformation objects
            filename: Output filename (optional)
            
        Returns:
            Path to the created summary report
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pharmaceutical_summary_{timestamp}.xlsx"
        
        stats = self._calculate_statistics(drugs)
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Summary overview
            overview_data = [
                ['Metric', 'Value'],
                ['Total Records', stats['general']['total_records']],
                ['Unique Drugs', stats['general']['unique_drugs']],
                ['Unique Manufacturers', stats['general']['unique_manufacturers']],
                ['Average Price (INR)', stats['pricing']['avg_price']],
                ['Price Range', f"{stats['pricing']['min_price']} - {stats['pricing']['max_price']}"],
            ]
            
            overview_df = pd.DataFrame(overview_data[1:], columns=overview_data[0])
            overview_df.to_excel(writer, sheet_name='Overview', index=False)
        
        self._apply_formatting(filename)
        return filename
