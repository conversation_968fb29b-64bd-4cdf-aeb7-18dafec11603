#!/usr/bin/env python3
"""
Installation script for the Pharmaceutical Chatbot system.
Handles setup, dependency installation, and initial data collection.
"""

import os
import sys
import subprocess
import time
from pathlib import Path


class ChatbotInstaller:
    """Handles installation and setup of the pharmaceutical chatbot."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.venv_path = self.project_root / "venv"
        
    def print_header(self, title):
        """Print a formatted header."""
        print("\n" + "="*60)
        print(f"  {title}")
        print("="*60)
    
    def print_step(self, step, description):
        """Print a step with formatting."""
        print(f"\n[{step}] {description}")
        print("-" * 40)
    
    def run_command(self, command, description="", check=True):
        """Run a command and handle errors."""
        print(f"Running: {command}")
        try:
            # Handle Windows paths with spaces by using list format for subprocess
            if isinstance(command, str):
                # For Windows, use shell=True with proper quoting
                if os.name == 'nt':
                    result = subprocess.run(command, shell=True, check=check,
                                          capture_output=True, text=True, cwd=str(self.project_root))
                else:
                    result = subprocess.run(command, shell=True, check=check,
                                          capture_output=True, text=True)
            else:
                # For list commands, don't use shell
                result = subprocess.run(command, check=check,
                                      capture_output=True, text=True, cwd=str(self.project_root))

            if result.stdout:
                print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Error: {e}")
            if e.stderr:
                print(f"Error details: {e.stderr}")
            return False
    
    def check_python_version(self):
        """Check if Python version is compatible."""
        self.print_step("1", "Checking Python Version")
        
        if sys.version_info < (3, 7):
            print("❌ Python 3.7 or higher is required!")
            print(f"Current version: {sys.version}")
            return False
        
        print(f"✅ Python {sys.version.split()[0]} - Compatible")
        return True
    
    def create_virtual_environment(self):
        """Create a virtual environment."""
        self.print_step("2", "Creating Virtual Environment")

        if self.venv_path.exists():
            print("✅ Virtual environment already exists")
            return True

        # Use list format to avoid path issues
        venv_command = [sys.executable, "-m", "venv", str(self.venv_path)]
        if self.run_command(venv_command):
            print("✅ Virtual environment created successfully")
            return True
        else:
            print("❌ Failed to create virtual environment")
            return False
    
    def activate_venv_command(self):
        """Get the command to activate virtual environment."""
        if os.name == 'nt':  # Windows
            return f"{self.venv_path}\\Scripts\\activate"
        else:  # Unix/Linux/Mac
            return f"source {self.venv_path}/bin/activate"
    
    def install_dependencies(self):
        """Install required dependencies."""
        self.print_step("3", "Installing Dependencies")

        # Get pip path
        if os.name == 'nt':  # Windows
            pip_path = self.venv_path / "Scripts" / "pip.exe"
        else:  # Unix/Linux/Mac
            pip_path = self.venv_path / "bin" / "pip"

        # Upgrade pip first using list format
        pip_upgrade_cmd = [str(pip_path), "install", "--upgrade", "pip"]
        if not self.run_command(pip_upgrade_cmd):
            print("❌ Failed to upgrade pip")
            return False

        # Install requirements using list format
        requirements_path = str(self.project_root / "requirements.txt")
        pip_install_cmd = [str(pip_path), "install", "-r", requirements_path]
        if not self.run_command(pip_install_cmd):
            print("❌ Failed to install dependencies")
            return False

        print("✅ Dependencies installed successfully")
        return True
    
    def setup_database(self):
        """Initialize the database."""
        self.print_step("4", "Setting Up Database")
        
        try:
            from src.database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            print("✅ Database initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Database setup failed: {e}")
            return False
    
    def collect_sample_data(self):
        """Collect sample pharmaceutical data."""
        self.print_step("5", "Collecting Sample Data")
        
        print("This step will collect pharmaceutical data from online sources.")
        print("It may take 10-30 minutes depending on your internet connection.")
        
        response = input("\nWould you like to collect data now? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            print("\nStarting data collection...")
            print("You can stop this process anytime with Ctrl+C")
            
            # Get python path in venv
            if os.name == 'nt':  # Windows
                python_path = self.venv_path / "Scripts" / "python"
            else:  # Unix/Linux/Mac
                python_path = self.venv_path / "bin" / "python"
            
            # Collect a small sample first
            if self.run_command(f"{python_path} collect_data.py --category pain_relief --limit 10", check=False):
                print("✅ Sample data collected successfully")
                
                # Ask if user wants to collect more
                more_response = input("\nCollect more comprehensive data? (y/n): ").lower().strip()
                if more_response in ['y', 'yes']:
                    print("Collecting comprehensive data... This may take 20-30 minutes.")
                    self.run_command(f"{python_path} collect_data.py --limit 15", check=False)
                
                return True
            else:
                print("⚠️  Data collection had some issues, but you can run it later")
                return True
        else:
            print("⚠️  Skipping data collection. You can run it later with:")
            print("   python collect_data.py")
            return True
    
    def test_installation(self):
        """Test the installation."""
        self.print_step("6", "Testing Installation")
        
        try:
            # Test imports
            from src.chatbot.intelligence import PharmaChatbot
            from src.database.db_manager import DatabaseManager
            from flask import Flask
            
            # Test basic functionality
            db_manager = DatabaseManager()
            stats = db_manager.get_database_stats()
            
            print(f"✅ Database connection: OK")
            print(f"✅ Total drugs in database: {stats['total_drugs']}")
            
            # Test chatbot
            chatbot = PharmaChatbot()
            test_response = chatbot.process_query("help")
            
            if test_response.get('type') == 'help':
                print("✅ Chatbot: OK")
            else:
                print("⚠️  Chatbot: Basic functionality working")
            
            print("✅ Installation test passed!")
            return True
            
        except Exception as e:
            print(f"❌ Installation test failed: {e}")
            return False
    
    def create_startup_scripts(self):
        """Create convenient startup scripts."""
        self.print_step("7", "Creating Startup Scripts")
        
        # Windows batch file
        if os.name == 'nt':
            batch_content = f"""@echo off
echo Starting Pharmaceutical Chatbot...
cd /d "{self.project_root}"
call "{self.venv_path}\\Scripts\\activate"
python app.py
pause
"""
            with open("start_chatbot.bat", "w") as f:
                f.write(batch_content)
            print("✅ Created start_chatbot.bat")
        
        # Unix shell script
        shell_content = f"""#!/bin/bash
echo "Starting Pharmaceutical Chatbot..."
cd "{self.project_root}"
source "{self.venv_path}/bin/activate"
python app.py
"""
        with open("start_chatbot.sh", "w") as f:
            f.write(shell_content)
        
        # Make shell script executable
        if os.name != 'nt':
            os.chmod("start_chatbot.sh", 0o755)
            print("✅ Created start_chatbot.sh")
        
        return True
    
    def print_completion_message(self):
        """Print installation completion message."""
        self.print_header("INSTALLATION COMPLETED SUCCESSFULLY! 🎉")
        
        print("Your Pharmaceutical Chatbot is ready to use!")
        print("\n📋 What's been set up:")
        print("  ✅ Virtual environment with all dependencies")
        print("  ✅ SQLite database for fast local queries")
        print("  ✅ Web interface with responsive design")
        print("  ✅ Intelligent chatbot with drug recommendations")
        
        print("\n🚀 How to start the chatbot:")
        if os.name == 'nt':
            print("  Option 1: Double-click 'start_chatbot.bat'")
            print("  Option 2: Run 'python app.py' in the activated environment")
        else:
            print("  Option 1: Run './start_chatbot.sh'")
            print("  Option 2: Run 'python app.py' in the activated environment")
        
        print("\n🌐 Access the chatbot:")
        print("  Open your browser and go to: http://localhost:5000")
        
        print("\n📊 Collect more data:")
        print("  python collect_data.py --category vitamins")
        print("  python collect_data.py  # Full collection")
        
        print("\n💡 Example queries to try:")
        print("  • 'What's the cheapest paracetamol?'")
        print("  • 'Compare aspirin prices'")
        print("  • 'Show me antibiotics under ₹200'")
        print("  • 'Tell me about side effects of ibuprofen'")
        
        print("\n🆘 Need help?")
        print("  • Type 'help' in the chatbot")
        print("  • Check the About page for more information")
        print("  • View README.md for detailed documentation")
        
        print("\n" + "="*60)
    
    def install(self):
        """Run the complete installation process."""
        self.print_header("PHARMACEUTICAL CHATBOT INSTALLER")
        print("This installer will set up everything you need to run the chatbot locally.")
        
        try:
            # Installation steps
            if not self.check_python_version():
                return False
            
            if not self.create_virtual_environment():
                return False
            
            if not self.install_dependencies():
                return False
            
            if not self.setup_database():
                return False
            
            if not self.collect_sample_data():
                return False
            
            if not self.test_installation():
                return False
            
            if not self.create_startup_scripts():
                return False
            
            self.print_completion_message()
            return True
            
        except KeyboardInterrupt:
            print("\n\n❌ Installation interrupted by user.")
            return False
        except Exception as e:
            print(f"\n\n❌ Installation failed: {e}")
            return False


def main():
    """Main installation entry point."""
    installer = ChatbotInstaller()
    
    print("Welcome to the Pharmaceutical Chatbot Installer!")
    print("This will set up a complete local chatbot system for drug price comparison.")
    
    response = input("\nProceed with installation? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        success = installer.install()
        if success:
            print("\n🎉 Installation completed successfully!")
            input("\nPress Enter to exit...")
        else:
            print("\n❌ Installation failed. Please check the errors above.")
            input("\nPress Enter to exit...")
    else:
        print("Installation cancelled.")


if __name__ == '__main__':
    main()
