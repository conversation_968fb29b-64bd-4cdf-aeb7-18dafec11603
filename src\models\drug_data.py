"""
Data models for pharmaceutical drug information.
Provides unified schema for drug data from multiple sources.
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class SourceWebsite(Enum):
    """Enumeration of supported pharmaceutical websites."""
    ONEMG = "1mg"
    APOLLO = "Apollo Pharmacy"
    INDIAMART = "IndiaMART"


class AvailabilityStatus(Enum):
    """Drug availability status."""
    IN_STOCK = "In Stock"
    OUT_OF_STOCK = "Out of Stock"
    LIMITED_STOCK = "Limited Stock"
    UNKNOWN = "Unknown"


@dataclass
class DrugInformation:
    """
    Unified data model for drug information from pharmaceutical websites.
    
    This model accommodates data from 1mg.com, Apollo Pharmacy, and IndiaMART
    with standardized field names and formats.
    """
    
    # Mandatory fields
    source_website: SourceWebsite
    scraped_at: datetime = field(default_factory=datetime.now)
    
    # Core drug information
    drug_name: Optional[str] = None
    brand_name: Optional[str] = None
    generic_name: Optional[str] = None
    manufacturer: Optional[str] = None
    composition: Optional[str] = None
    
    # Product details
    dosage_form: Optional[str] = None  # tablet, capsule, syrup, etc.
    strength: Optional[str] = None  # mg, ml, etc.
    pack_size: Optional[str] = None  # number of units
    
    # Pricing information
    mrp: Optional[float] = None  # Maximum Retail Price
    selling_price: Optional[float] = None  # Current selling price
    discount_percentage: Optional[float] = None
    price_per_unit: Optional[float] = None
    currency: str = "INR"
    
    # Availability and stock
    availability_status: AvailabilityStatus = AvailabilityStatus.UNKNOWN
    stock_quantity: Optional[int] = None
    
    # Medical information
    therapeutic_class: Optional[str] = None
    drug_category: Optional[str] = None  # prescription, OTC, etc.
    uses_indications: Optional[List[str]] = field(default_factory=list)
    side_effects: Optional[List[str]] = field(default_factory=list)
    contraindications: Optional[List[str]] = field(default_factory=list)
    dosage_instructions: Optional[str] = None
    
    # Additional details
    product_url: Optional[str] = None
    image_urls: Optional[List[str]] = field(default_factory=list)
    product_id: Optional[str] = None  # Website-specific product ID
    
    # Regulatory information
    drug_license_number: Optional[str] = None
    expiry_date: Optional[str] = None
    batch_number: Optional[str] = None
    
    # Seller information (especially for IndiaMART)
    seller_name: Optional[str] = None
    seller_location: Optional[str] = None
    seller_rating: Optional[float] = None
    
    # Additional metadata
    additional_info: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the drug information to a dictionary for export."""
        data = {}
        
        # Convert enum values to strings
        data['source_website'] = self.source_website.value
        data['availability_status'] = self.availability_status.value
        data['scraped_at'] = self.scraped_at.isoformat()
        
        # Add all other fields
        for field_name, field_value in self.__dict__.items():
            if field_name not in ['source_website', 'availability_status', 'scraped_at']:
                if isinstance(field_value, list):
                    data[field_name] = '; '.join(str(item) for item in field_value) if field_value else None
                elif isinstance(field_value, dict):
                    data[field_name] = str(field_value) if field_value else None
                else:
                    data[field_name] = field_value
        
        return data
    
    @classmethod
    def get_column_headers(cls) -> List[str]:
        """Get standardized column headers for Excel export."""
        return [
            'source_website',
            'scraped_at',
            'drug_name',
            'brand_name',
            'generic_name',
            'manufacturer',
            'composition',
            'dosage_form',
            'strength',
            'pack_size',
            'mrp',
            'selling_price',
            'discount_percentage',
            'price_per_unit',
            'currency',
            'availability_status',
            'stock_quantity',
            'therapeutic_class',
            'drug_category',
            'uses_indications',
            'side_effects',
            'contraindications',
            'dosage_instructions',
            'product_url',
            'image_urls',
            'product_id',
            'drug_license_number',
            'expiry_date',
            'batch_number',
            'seller_name',
            'seller_location',
            'seller_rating',
            'additional_info'
        ]


@dataclass
class ScrapingMetadata:
    """Metadata about the scraping session."""
    
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_records_scraped: int = 0
    records_by_source: Dict[str, int] = field(default_factory=dict)
    errors_encountered: List[str] = field(default_factory=list)
    scraping_parameters: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary for export."""
        return {
            'session_id': self.session_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'total_records_scraped': self.total_records_scraped,
            'records_by_source': self.records_by_source,
            'errors_count': len(self.errors_encountered),
            'scraping_parameters': self.scraping_parameters
        }
