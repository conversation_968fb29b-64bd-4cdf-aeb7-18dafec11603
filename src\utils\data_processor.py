"""
Data processing and standardization utilities for pharmaceutical data.
Provides functions to clean, validate, and standardize drug information.
"""

import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..models.drug_data import DrugInformation, AvailabilityStatus


class DataProcessor:
    """
    Handles data cleaning, standardization, and validation for pharmaceutical data.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Common drug name patterns for standardization
        self.drug_name_patterns = {
            r'\b(tab|tablet|cap|capsule|syrup|injection|cream|ointment)\b': '',
            r'\b\d+mg\b': '',
            r'\b\d+ml\b': '',
            r'\([^)]*\)': '',  # Remove content in parentheses
        }
        
        # Dosage form standardization
        self.dosage_form_mapping = {
            'tab': 'Tablet',
            'tablet': 'Tablet',
            'tablets': 'Tablet',
            'cap': 'Capsule',
            'capsule': 'Capsule',
            'capsules': 'Capsule',
            'syrup': 'Syrup',
            'liquid': 'Syrup',
            'injection': 'Injection',
            'inj': 'Injection',
            'cream': 'Cream',
            'ointment': 'Ointment',
            'gel': 'Gel',
            'drops': 'Drops',
            'suspension': 'Suspension',
            'powder': 'Powder',
        }
        
        # Therapeutic class standardization
        self.therapeutic_class_mapping = {
            'analgesic': 'Analgesics',
            'antibiotic': 'Antibiotics',
            'antacid': 'Antacids',
            'vitamin': 'Vitamins',
            'supplement': 'Supplements',
            'cardiovascular': 'Cardiovascular',
            'respiratory': 'Respiratory',
            'gastrointestinal': 'Gastrointestinal',
            'neurological': 'Neurological',
            'dermatological': 'Dermatological',
        }
    
    def standardize_drug_name(self, drug_name: str) -> str:
        """
        Standardize drug name by removing common suffixes and normalizing format.
        
        Args:
            drug_name: Raw drug name
            
        Returns:
            Standardized drug name
        """
        if not drug_name:
            return None
        
        # Convert to title case
        standardized = drug_name.strip().title()
        
        # Apply cleaning patterns
        for pattern, replacement in self.drug_name_patterns.items():
            standardized = re.sub(pattern, replacement, standardized, flags=re.IGNORECASE)
        
        # Clean up extra spaces
        standardized = ' '.join(standardized.split())
        
        return standardized if standardized else None
    
    def standardize_dosage_form(self, dosage_form: str) -> str:
        """
        Standardize dosage form to consistent format.
        
        Args:
            dosage_form: Raw dosage form
            
        Returns:
            Standardized dosage form
        """
        if not dosage_form:
            return None
        
        cleaned = dosage_form.lower().strip()
        
        # Check for exact matches first
        if cleaned in self.dosage_form_mapping:
            return self.dosage_form_mapping[cleaned]
        
        # Check for partial matches
        for key, value in self.dosage_form_mapping.items():
            if key in cleaned:
                return value
        
        # Return title case if no mapping found
        return dosage_form.title()
    
    def standardize_therapeutic_class(self, therapeutic_class: str) -> str:
        """
        Standardize therapeutic class to consistent format.
        
        Args:
            therapeutic_class: Raw therapeutic class
            
        Returns:
            Standardized therapeutic class
        """
        if not therapeutic_class:
            return None
        
        cleaned = therapeutic_class.lower().strip()
        
        # Check for partial matches
        for key, value in self.therapeutic_class_mapping.items():
            if key in cleaned:
                return value
        
        # Return title case if no mapping found
        return therapeutic_class.title()
    
    def standardize_price(self, price: float, currency: str = 'INR') -> Dict[str, Any]:
        """
        Standardize price information.
        
        Args:
            price: Price value
            currency: Currency code
            
        Returns:
            Dictionary with standardized price information
        """
        if price is None:
            return {'price': None, 'currency': currency, 'formatted': None}
        
        try:
            standardized_price = round(float(price), 2)
            formatted = f"{currency} {standardized_price:,.2f}"
            
            return {
                'price': standardized_price,
                'currency': currency,
                'formatted': formatted
            }
        except (ValueError, TypeError):
            return {'price': None, 'currency': currency, 'formatted': None}
    
    def extract_strength_info(self, text: str) -> Dict[str, str]:
        """
        Extract strength/dosage information from text.
        
        Args:
            text: Text containing strength information
            
        Returns:
            Dictionary with extracted strength information
        """
        if not text:
            return {'strength': None, 'unit': None}
        
        # Common strength patterns
        patterns = [
            r'(\d+(?:\.\d+)?)\s*(mg|mcg|g|ml|iu|units?)',
            r'(\d+(?:\.\d+)?)\s*(milligram|microgram|gram|milliliter)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.lower())
            if match:
                return {
                    'strength': match.group(1),
                    'unit': match.group(2)
                }
        
        return {'strength': text.strip(), 'unit': None}
    
    def validate_drug_information(self, drug_info: DrugInformation) -> Dict[str, List[str]]:
        """
        Validate drug information and return validation errors.
        
        Args:
            drug_info: DrugInformation object to validate
            
        Returns:
            Dictionary with validation errors by field
        """
        errors = {}
        
        # Required fields validation
        if not drug_info.drug_name:
            errors.setdefault('drug_name', []).append('Drug name is required')
        
        if not drug_info.source_website:
            errors.setdefault('source_website', []).append('Source website is required')
        
        # Price validation
        if drug_info.selling_price is not None and drug_info.selling_price < 0:
            errors.setdefault('selling_price', []).append('Selling price cannot be negative')
        
        if drug_info.mrp is not None and drug_info.mrp < 0:
            errors.setdefault('mrp', []).append('MRP cannot be negative')
        
        if (drug_info.selling_price is not None and drug_info.mrp is not None and 
            drug_info.selling_price > drug_info.mrp):
            errors.setdefault('price', []).append('Selling price cannot be greater than MRP')
        
        # URL validation
        if drug_info.product_url and not self._is_valid_url(drug_info.product_url):
            errors.setdefault('product_url', []).append('Invalid product URL format')
        
        # Rating validation
        if drug_info.seller_rating is not None and (drug_info.seller_rating < 0 or drug_info.seller_rating > 5):
            errors.setdefault('seller_rating', []).append('Seller rating must be between 0 and 5')
        
        return errors
    
    def _is_valid_url(self, url: str) -> bool:
        """Check if URL is valid."""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    def process_drug_information(self, drug_info: DrugInformation) -> DrugInformation:
        """
        Process and standardize a DrugInformation object.
        
        Args:
            drug_info: Raw DrugInformation object
            
        Returns:
            Processed and standardized DrugInformation object
        """
        try:
            # Standardize drug name
            if drug_info.drug_name:
                drug_info.drug_name = self.standardize_drug_name(drug_info.drug_name)
            
            # Standardize dosage form
            if drug_info.dosage_form:
                drug_info.dosage_form = self.standardize_dosage_form(drug_info.dosage_form)
            
            # Standardize therapeutic class
            if drug_info.therapeutic_class:
                drug_info.therapeutic_class = self.standardize_therapeutic_class(drug_info.therapeutic_class)
            
            # Process strength information
            if drug_info.strength:
                strength_info = self.extract_strength_info(drug_info.strength)
                if strength_info['strength'] and strength_info['unit']:
                    drug_info.strength = f"{strength_info['strength']} {strength_info['unit']}"
            
            # Calculate discount percentage if not present
            if (drug_info.mrp and drug_info.selling_price and 
                drug_info.discount_percentage is None):
                drug_info.discount_percentage = ((drug_info.mrp - drug_info.selling_price) / drug_info.mrp) * 100
            
            # Calculate price per unit if pack size is available
            if (drug_info.selling_price and drug_info.pack_size and 
                drug_info.price_per_unit is None):
                try:
                    pack_size_num = float(re.search(r'\d+', str(drug_info.pack_size)).group())
                    drug_info.price_per_unit = drug_info.selling_price / pack_size_num
                except (AttributeError, ValueError, ZeroDivisionError):
                    pass
            
            # Clean and standardize text fields
            text_fields = ['manufacturer', 'composition', 'seller_name', 'seller_location']
            for field in text_fields:
                value = getattr(drug_info, field)
                if value:
                    setattr(drug_info, field, ' '.join(value.strip().split()))
            
            return drug_info
            
        except Exception as e:
            self.logger.error(f"Error processing drug information: {str(e)}")
            return drug_info
    
    def deduplicate_drugs(self, drugs: List[DrugInformation]) -> List[DrugInformation]:
        """
        Remove duplicate drug entries based on name and source.
        
        Args:
            drugs: List of DrugInformation objects
            
        Returns:
            Deduplicated list of DrugInformation objects
        """
        seen = set()
        deduplicated = []
        
        for drug in drugs:
            # Create a key based on drug name and source
            key = (
                drug.drug_name.lower() if drug.drug_name else '',
                drug.source_website.value if drug.source_website else '',
                drug.manufacturer.lower() if drug.manufacturer else ''
            )
            
            if key not in seen:
                seen.add(key)
                deduplicated.append(drug)
            else:
                self.logger.debug(f"Removing duplicate: {drug.drug_name} from {drug.source_website.value}")
        
        self.logger.info(f"Removed {len(drugs) - len(deduplicated)} duplicates")
        return deduplicated
