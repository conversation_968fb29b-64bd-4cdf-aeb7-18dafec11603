"""
1mg.com scraper for extracting pharmaceutical drug information.
"""

import re
from typing import List, Optional
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..models.drug_data import DrugInformation, SourceWebsite, AvailabilityStatus


class OneMgScraper(BaseScraper):
    """Scraper for 1mg.com pharmaceutical website."""
    
    def __init__(self, config):
        super().__init__(config)
        self.base_url = self.config.get('websites', {}).get('onemg', {}).get('base_url', 'https://www.1mg.com')
        self.search_url = self.config.get('websites', {}).get('onemg', {}).get('search_url', 'https://www.1mg.com/search/all')
    
    def get_source_website(self) -> SourceWebsite:
        """Return the source website enum."""
        return SourceWebsite.ONEMG
    
    def search_drugs(self, query: str, limit: int = 50) -> List[str]:
        """
        Search for drugs on 1mg.com and return product URLs.
        
        Args:
            query: Search query for drugs
            limit: Maximum number of results to return
            
        Returns:
            List of product URLs
        """
        product_urls = []
        
        try:
            # Construct search URL
            search_params = f"?name={quote(query)}"
            search_url = f"{self.search_url}{search_params}"
            
            self.logger.info(f"Searching 1mg for: {query}")
            response = self.make_request(search_url)
            
            if not response:
                return product_urls
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find product links - 1mg uses specific CSS classes for product cards
            product_links = soup.find_all('a', class_=re.compile(r'style__product-link'))
            
            if not product_links:
                # Fallback: look for any links containing '/drugs/'
                product_links = soup.find_all('a', href=re.compile(r'/drugs/'))
            
            for link in product_links[:limit]:
                href = link.get('href')
                if href:
                    full_url = urljoin(self.base_url, href)
                    if self.is_valid_url(full_url):
                        product_urls.append(full_url)
            
            self.logger.info(f"Found {len(product_urls)} product URLs for query: {query}")
            
        except Exception as e:
            self.logger.error(f"Error searching 1mg for '{query}': {str(e)}")
            self.errors.append(f"Search error for '{query}': {str(e)}")
        
        return product_urls
    
    def scrape_drug_details(self, product_url: str) -> Optional[DrugInformation]:
        """
        Scrape detailed drug information from a 1mg product page.
        
        Args:
            product_url: URL of the product page
            
        Returns:
            DrugInformation object or None if scraping failed
        """
        try:
            self.logger.debug(f"Scraping 1mg product: {product_url}")
            response = self.make_request(product_url)
            
            if not response:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Initialize drug information
            drug_info = DrugInformation(
                source_website=self.get_source_website(),
                product_url=product_url
            )
            
            # Extract drug name
            drug_name_elem = soup.find('h1', class_=re.compile(r'DrugHeader__title'))
            if not drug_name_elem:
                drug_name_elem = soup.find('h1')
            
            if drug_name_elem:
                drug_info.drug_name = self.clean_text(drug_name_elem.get_text())
            
            # Extract manufacturer
            manufacturer_elem = soup.find('a', class_=re.compile(r'DrugHeader__manufacturer'))
            if manufacturer_elem:
                drug_info.manufacturer = self.clean_text(manufacturer_elem.get_text())
            
            # Extract composition
            composition_elem = soup.find('div', class_=re.compile(r'saltInfo'))
            if not composition_elem:
                composition_elem = soup.find('span', string=re.compile(r'Salt Composition', re.I))
                if composition_elem:
                    composition_elem = composition_elem.find_next('span')
            
            if composition_elem:
                drug_info.composition = self.clean_text(composition_elem.get_text())
            
            # Extract price information
            price_elem = soup.find('span', class_=re.compile(r'PriceBoxPlanOption__offer-price'))
            if not price_elem:
                price_elem = soup.find('span', class_=re.compile(r'price'))
            
            if price_elem:
                price_text = price_elem.get_text()
                drug_info.selling_price = self.extract_price(price_text)
            
            # Extract MRP
            mrp_elem = soup.find('span', class_=re.compile(r'PriceBoxPlanOption__stike-through'))
            if mrp_elem:
                mrp_text = mrp_elem.get_text()
                drug_info.mrp = self.extract_price(mrp_text)
            
            # Calculate discount if both prices available
            if drug_info.mrp and drug_info.selling_price:
                drug_info.discount_percentage = ((drug_info.mrp - drug_info.selling_price) / drug_info.mrp) * 100
            
            # Extract pack size and dosage form
            pack_info_elem = soup.find('div', class_=re.compile(r'DrugHeader__meta-value'))
            if pack_info_elem:
                pack_text = pack_info_elem.get_text()
                # Try to extract pack size and form
                if 'tablet' in pack_text.lower():
                    drug_info.dosage_form = 'Tablet'
                elif 'capsule' in pack_text.lower():
                    drug_info.dosage_form = 'Capsule'
                elif 'syrup' in pack_text.lower():
                    drug_info.dosage_form = 'Syrup'
                
                # Extract pack size (number of units)
                pack_match = re.search(r'(\d+)\s*(tablet|capsule|ml)', pack_text.lower())
                if pack_match:
                    drug_info.pack_size = pack_match.group(1)
            
            # Extract availability status
            availability_elem = soup.find('div', class_=re.compile(r'available|stock'))
            if availability_elem:
                availability_text = availability_elem.get_text().lower()
                if 'in stock' in availability_text or 'available' in availability_text:
                    drug_info.availability_status = AvailabilityStatus.IN_STOCK
                elif 'out of stock' in availability_text:
                    drug_info.availability_status = AvailabilityStatus.OUT_OF_STOCK
            
            # Extract uses/indications
            uses_section = soup.find('div', id=re.compile(r'uses|indication'))
            if uses_section:
                uses_text = uses_section.get_text()
                drug_info.uses_indications = [self.clean_text(uses_text)]
            
            # Extract side effects
            side_effects_section = soup.find('div', id=re.compile(r'side.effects'))
            if side_effects_section:
                side_effects_text = side_effects_section.get_text()
                drug_info.side_effects = [self.clean_text(side_effects_text)]
            
            # Extract therapeutic class
            therapeutic_elem = soup.find('span', string=re.compile(r'Therapeutic Class', re.I))
            if therapeutic_elem:
                therapeutic_value = therapeutic_elem.find_next('span')
                if therapeutic_value:
                    drug_info.therapeutic_class = self.clean_text(therapeutic_value.get_text())
            
            # Extract product ID from URL
            product_id_match = re.search(r'/drugs/([^/]+)', product_url)
            if product_id_match:
                drug_info.product_id = product_id_match.group(1)
            
            # Extract image URLs
            img_elements = soup.find_all('img', src=re.compile(r'drug|product'))
            drug_info.image_urls = [img.get('src') for img in img_elements if img.get('src')]
            
            self.logger.debug(f"Successfully scraped drug: {drug_info.drug_name}")
            return drug_info
            
        except Exception as e:
            self.logger.error(f"Error scraping 1mg product {product_url}: {str(e)}")
            self.errors.append(f"Product scraping error for {product_url}: {str(e)}")
            return None
