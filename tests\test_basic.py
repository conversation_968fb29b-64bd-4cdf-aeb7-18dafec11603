"""
Basic tests for the pharmaceutical data scraper.
"""

import unittest
import tempfile
import os
from datetime import datetime

from src.models.drug_data import DrugInformation, SourceWebsite, AvailabilityStatus, ScrapingMetadata
from src.utils.data_processor import DataProcessor
from src.utils.excel_exporter import ExcelExporter


class TestDrugDataModel(unittest.TestCase):
    """Test the drug data model."""
    
    def test_drug_information_creation(self):
        """Test creating a DrugInformation object."""
        drug = DrugInformation(
            source_website=SourceWebsite.ONEMG,
            drug_name="Paracetamol",
            manufacturer="Test Pharma",
            selling_price=50.0,
            mrp=60.0
        )
        
        self.assertEqual(drug.source_website, SourceWebsite.ONEMG)
        self.assertEqual(drug.drug_name, "Paracetamol")
        self.assertEqual(drug.manufacturer, "Test Pharma")
        self.assertEqual(drug.selling_price, 50.0)
        self.assertEqual(drug.mrp, 60.0)
        self.assertEqual(drug.currency, "INR")
    
    def test_drug_to_dict(self):
        """Test converting drug information to dictionary."""
        drug = DrugInformation(
            source_website=SourceWebsite.APOLLO,
            drug_name="Aspirin",
            uses_indications=["Pain relief", "Fever reduction"]
        )
        
        drug_dict = drug.to_dict()
        
        self.assertEqual(drug_dict['source_website'], "Apollo Pharmacy")
        self.assertEqual(drug_dict['drug_name'], "Aspirin")
        self.assertEqual(drug_dict['uses_indications'], "Pain relief; Fever reduction")
    
    def test_column_headers(self):
        """Test getting column headers."""
        headers = DrugInformation.get_column_headers()
        
        self.assertIn('source_website', headers)
        self.assertIn('drug_name', headers)
        self.assertIn('manufacturer', headers)
        self.assertIn('selling_price', headers)
        self.assertIn('mrp', headers)


class TestDataProcessor(unittest.TestCase):
    """Test the data processor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = DataProcessor()
    
    def test_standardize_drug_name(self):
        """Test drug name standardization."""
        # Test basic standardization
        result = self.processor.standardize_drug_name("paracetamol tablet 500mg")
        self.assertNotIn("tablet", result.lower())
        self.assertNotIn("500mg", result)
        
        # Test title case
        result = self.processor.standardize_drug_name("aspirin")
        self.assertEqual(result, "Aspirin")
    
    def test_standardize_dosage_form(self):
        """Test dosage form standardization."""
        self.assertEqual(self.processor.standardize_dosage_form("tab"), "Tablet")
        self.assertEqual(self.processor.standardize_dosage_form("capsule"), "Capsule")
        self.assertEqual(self.processor.standardize_dosage_form("syrup"), "Syrup")
    
    def test_extract_price(self):
        """Test price extraction from base scraper."""
        from src.scrapers.base_scraper import BaseScraper
        
        # Create a mock config
        config = {'scraping': {'delay_between_requests': 1}}
        
        # Create a concrete implementation for testing
        class TestScraper(BaseScraper):
            def get_source_website(self):
                return SourceWebsite.ONEMG
            
            def search_drugs(self, query, limit=50):
                return []
            
            def scrape_drug_details(self, product_url):
                return None
        
        scraper = TestScraper(config)
        
        # Test price extraction
        self.assertEqual(scraper.extract_price("₹50.00"), 50.0)
        self.assertEqual(scraper.extract_price("Rs. 125.50"), 125.5)
        self.assertEqual(scraper.extract_price("Price: 75"), 75.0)
        self.assertIsNone(scraper.extract_price("No price"))
    
    def test_validate_drug_information(self):
        """Test drug information validation."""
        # Valid drug
        drug = DrugInformation(
            source_website=SourceWebsite.ONEMG,
            drug_name="Paracetamol",
            selling_price=50.0,
            mrp=60.0
        )
        
        errors = self.processor.validate_drug_information(drug)
        self.assertEqual(len(errors), 0)
        
        # Invalid drug (selling price > MRP)
        drug.selling_price = 70.0
        errors = self.processor.validate_drug_information(drug)
        self.assertIn('price', errors)
    
    def test_process_drug_information(self):
        """Test processing drug information."""
        drug = DrugInformation(
            source_website=SourceWebsite.ONEMG,
            drug_name="paracetamol tablet",
            dosage_form="tab",
            mrp=60.0,
            selling_price=50.0
        )
        
        processed = self.processor.process_drug_information(drug)
        
        # Check standardization
        self.assertEqual(processed.dosage_form, "Tablet")
        self.assertIsNotNone(processed.discount_percentage)
        self.assertAlmostEqual(processed.discount_percentage, 16.67, places=1)


class TestExcelExporter(unittest.TestCase):
    """Test the Excel exporter."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'output': {
                'include_metadata': True,
                'max_records_per_sheet': 1000000
            }
        }
        self.exporter = ExcelExporter(self.config)
    
    def test_export_to_excel(self):
        """Test exporting data to Excel."""
        # Create test data
        drugs = [
            DrugInformation(
                source_website=SourceWebsite.ONEMG,
                drug_name="Paracetamol",
                manufacturer="Test Pharma",
                selling_price=50.0
            ),
            DrugInformation(
                source_website=SourceWebsite.APOLLO,
                drug_name="Aspirin",
                manufacturer="Another Pharma",
                selling_price=75.0
            )
        ]
        
        metadata = ScrapingMetadata(
            session_id="test-session",
            start_time=datetime.now(),
            total_records_scraped=2
        )
        metadata.records_by_source = {"1mg": 1, "Apollo Pharmacy": 1}
        
        # Export to temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            try:
                output_file = self.exporter.export_to_excel(drugs, metadata, tmp.name)
                
                # Check file was created
                self.assertTrue(os.path.exists(output_file))
                self.assertTrue(output_file.endswith('.xlsx'))
                
                # Basic file size check (should not be empty)
                self.assertGreater(os.path.getsize(output_file), 1000)
                
            finally:
                # Clean up
                if os.path.exists(tmp.name):
                    os.unlink(tmp.name)


class TestScrapingMetadata(unittest.TestCase):
    """Test the scraping metadata model."""
    
    def test_metadata_creation(self):
        """Test creating scraping metadata."""
        metadata = ScrapingMetadata(
            session_id="test-123",
            start_time=datetime.now(),
            total_records_scraped=100
        )
        
        self.assertEqual(metadata.session_id, "test-123")
        self.assertEqual(metadata.total_records_scraped, 100)
        self.assertIsInstance(metadata.records_by_source, dict)
        self.assertIsInstance(metadata.errors_encountered, list)
    
    def test_metadata_to_dict(self):
        """Test converting metadata to dictionary."""
        metadata = ScrapingMetadata(
            session_id="test-456",
            start_time=datetime.now(),
            total_records_scraped=50
        )
        metadata.records_by_source = {"1mg": 30, "Apollo": 20}
        
        metadata_dict = metadata.to_dict()
        
        self.assertEqual(metadata_dict['session_id'], "test-456")
        self.assertEqual(metadata_dict['total_records_scraped'], 50)
        self.assertEqual(metadata_dict['records_by_source'], {"1mg": 30, "Apollo": 20})


if __name__ == '__main__':
    # Create tests directory if it doesn't exist
    os.makedirs('tests', exist_ok=True)
    
    # Run tests
    unittest.main(verbosity=2)
