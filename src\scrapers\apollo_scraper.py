"""
Apollo Pharmacy scraper for extracting pharmaceutical drug information.
"""

import re
from typing import List, Optional
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..models.drug_data import DrugInformation, SourceWebsite, AvailabilityStatus


class ApolloScraper(BaseScraper):
    """Scraper for Apollo Pharmacy website."""
    
    def __init__(self, config):
        super().__init__(config)
        self.base_url = self.config.get('websites', {}).get('apollo', {}).get('base_url', 'https://www.apollopharmacy.in')
        self.search_url = self.config.get('websites', {}).get('apollo', {}).get('search_url', 'https://www.apollopharmacy.in/search-medicines')
    
    def get_source_website(self) -> SourceWebsite:
        """Return the source website enum."""
        return SourceWebsite.APOLLO
    
    def search_drugs(self, query: str, limit: int = 50) -> List[str]:
        """
        Search for drugs on Apollo Pharmacy and return product URLs.
        
        Args:
            query: Search query for drugs
            limit: Maximum number of results to return
            
        Returns:
            List of product URLs
        """
        product_urls = []
        
        try:
            # Construct search URL
            search_params = f"/{quote(query)}"
            search_url = f"{self.search_url}{search_params}"
            
            self.logger.info(f"Searching Apollo Pharmacy for: {query}")
            response = self.make_request(search_url)
            
            if not response:
                return product_urls
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find product links - Apollo uses specific patterns
            product_links = soup.find_all('a', href=re.compile(r'/otc/|/prescription/'))
            
            if not product_links:
                # Fallback: look for product card links
                product_links = soup.find_all('a', class_=re.compile(r'product|item'))
            
            for link in product_links[:limit]:
                href = link.get('href')
                if href:
                    full_url = urljoin(self.base_url, href)
                    if self.is_valid_url(full_url):
                        product_urls.append(full_url)
            
            self.logger.info(f"Found {len(product_urls)} product URLs for query: {query}")
            
        except Exception as e:
            self.logger.error(f"Error searching Apollo for '{query}': {str(e)}")
            self.errors.append(f"Search error for '{query}': {str(e)}")
        
        return product_urls
    
    def scrape_drug_details(self, product_url: str) -> Optional[DrugInformation]:
        """
        Scrape detailed drug information from an Apollo Pharmacy product page.
        
        Args:
            product_url: URL of the product page
            
        Returns:
            DrugInformation object or None if scraping failed
        """
        try:
            self.logger.debug(f"Scraping Apollo product: {product_url}")
            response = self.make_request(product_url)
            
            if not response:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Initialize drug information
            drug_info = DrugInformation(
                source_website=self.get_source_website(),
                product_url=product_url
            )
            
            # Extract drug name
            drug_name_elem = soup.find('h1', class_=re.compile(r'ProductTitle|product-title'))
            if not drug_name_elem:
                drug_name_elem = soup.find('h1')
            
            if drug_name_elem:
                drug_info.drug_name = self.clean_text(drug_name_elem.get_text())
            
            # Extract manufacturer
            manufacturer_elem = soup.find('span', class_=re.compile(r'manufacturer|brand'))
            if not manufacturer_elem:
                # Look for "Manufactured by" or "Brand:" labels
                manufacturer_label = soup.find(string=re.compile(r'Manufactured by|Brand:', re.I))
                if manufacturer_label:
                    manufacturer_elem = manufacturer_label.find_next()
            
            if manufacturer_elem:
                drug_info.manufacturer = self.clean_text(manufacturer_elem.get_text())
            
            # Extract composition
            composition_elem = soup.find('div', class_=re.compile(r'composition|salt'))
            if not composition_elem:
                composition_label = soup.find(string=re.compile(r'Composition|Salt|Active Ingredient', re.I))
                if composition_label:
                    composition_elem = composition_label.find_next()
            
            if composition_elem:
                drug_info.composition = self.clean_text(composition_elem.get_text())
            
            # Extract price information
            price_elem = soup.find('span', class_=re.compile(r'price|cost'))
            if not price_elem:
                price_elem = soup.find('div', class_=re.compile(r'price'))
            
            if price_elem:
                price_text = price_elem.get_text()
                drug_info.selling_price = self.extract_price(price_text)
            
            # Extract MRP
            mrp_elem = soup.find('span', class_=re.compile(r'mrp|original'))
            if not mrp_elem:
                mrp_label = soup.find(string=re.compile(r'MRP|M\.R\.P', re.I))
                if mrp_label:
                    mrp_elem = mrp_label.find_next()
            
            if mrp_elem:
                mrp_text = mrp_elem.get_text()
                drug_info.mrp = self.extract_price(mrp_text)
            
            # Calculate discount
            if drug_info.mrp and drug_info.selling_price:
                drug_info.discount_percentage = ((drug_info.mrp - drug_info.selling_price) / drug_info.mrp) * 100
            
            # Extract pack size and dosage form
            pack_info_elem = soup.find('div', class_=re.compile(r'pack|size|quantity'))
            if not pack_info_elem:
                pack_label = soup.find(string=re.compile(r'Pack Size|Quantity', re.I))
                if pack_label:
                    pack_info_elem = pack_label.find_next()
            
            if pack_info_elem:
                pack_text = pack_info_elem.get_text()
                
                # Determine dosage form
                if 'tablet' in pack_text.lower():
                    drug_info.dosage_form = 'Tablet'
                elif 'capsule' in pack_text.lower():
                    drug_info.dosage_form = 'Capsule'
                elif 'syrup' in pack_text.lower() or 'liquid' in pack_text.lower():
                    drug_info.dosage_form = 'Syrup'
                elif 'injection' in pack_text.lower():
                    drug_info.dosage_form = 'Injection'
                elif 'cream' in pack_text.lower() or 'ointment' in pack_text.lower():
                    drug_info.dosage_form = 'Topical'
                
                # Extract pack size
                pack_match = re.search(r'(\d+)\s*(tablet|capsule|ml|gm)', pack_text.lower())
                if pack_match:
                    drug_info.pack_size = pack_match.group(1)
            
            # Extract strength/dosage
            strength_elem = soup.find('span', class_=re.compile(r'strength|dosage'))
            if not strength_elem:
                strength_label = soup.find(string=re.compile(r'Strength|Dosage', re.I))
                if strength_label:
                    strength_elem = strength_label.find_next()
            
            if strength_elem:
                drug_info.strength = self.clean_text(strength_elem.get_text())
            
            # Extract availability status
            availability_elem = soup.find('div', class_=re.compile(r'stock|availability'))
            if not availability_elem:
                availability_elem = soup.find('span', class_=re.compile(r'stock|available'))
            
            if availability_elem:
                availability_text = availability_elem.get_text().lower()
                if 'in stock' in availability_text or 'available' in availability_text:
                    drug_info.availability_status = AvailabilityStatus.IN_STOCK
                elif 'out of stock' in availability_text or 'not available' in availability_text:
                    drug_info.availability_status = AvailabilityStatus.OUT_OF_STOCK
                elif 'limited' in availability_text:
                    drug_info.availability_status = AvailabilityStatus.LIMITED_STOCK
            
            # Extract uses/indications
            uses_elem = soup.find('div', class_=re.compile(r'uses|indication|benefit'))
            if not uses_elem:
                uses_label = soup.find(string=re.compile(r'Uses|Indication|Benefits', re.I))
                if uses_label:
                    uses_elem = uses_label.find_next()
            
            if uses_elem:
                uses_text = self.clean_text(uses_elem.get_text())
                if uses_text:
                    drug_info.uses_indications = [uses_text]
            
            # Extract side effects
            side_effects_elem = soup.find('div', class_=re.compile(r'side.effect|adverse'))
            if not side_effects_elem:
                side_effects_label = soup.find(string=re.compile(r'Side Effects|Adverse', re.I))
                if side_effects_label:
                    side_effects_elem = side_effects_label.find_next()
            
            if side_effects_elem:
                side_effects_text = self.clean_text(side_effects_elem.get_text())
                if side_effects_text:
                    drug_info.side_effects = [side_effects_text]
            
            # Extract therapeutic class
            therapeutic_elem = soup.find('span', class_=re.compile(r'therapeutic|category'))
            if not therapeutic_elem:
                therapeutic_label = soup.find(string=re.compile(r'Therapeutic Class|Category', re.I))
                if therapeutic_label:
                    therapeutic_elem = therapeutic_label.find_next()
            
            if therapeutic_elem:
                drug_info.therapeutic_class = self.clean_text(therapeutic_elem.get_text())
            
            # Extract drug category (prescription/OTC)
            category_elem = soup.find('span', class_=re.compile(r'prescription|otc'))
            if category_elem:
                category_text = category_elem.get_text().lower()
                if 'prescription' in category_text:
                    drug_info.drug_category = 'Prescription'
                elif 'otc' in category_text or 'over the counter' in category_text:
                    drug_info.drug_category = 'OTC'
            
            # Extract product ID from URL
            product_id_match = re.search(r'/([^/]+)/?$', product_url)
            if product_id_match:
                drug_info.product_id = product_id_match.group(1)
            
            # Extract image URLs
            img_elements = soup.find_all('img', src=re.compile(r'product|drug'))
            drug_info.image_urls = [urljoin(self.base_url, img.get('src')) for img in img_elements if img.get('src')]
            
            self.logger.debug(f"Successfully scraped drug: {drug_info.drug_name}")
            return drug_info
            
        except Exception as e:
            self.logger.error(f"Error scraping Apollo product {product_url}: {str(e)}")
            self.errors.append(f"Product scraping error for {product_url}: {str(e)}")
            return None
