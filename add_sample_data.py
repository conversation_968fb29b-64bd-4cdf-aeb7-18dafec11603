#!/usr/bin/env python3
"""
Add comprehensive sample data to the pharmaceutical database.
Run this if your chatbot shows "No results found".
"""

from src.database.db_manager import DatabaseManager
from src.models.drug_data import DrugInformation, SourceWebsite, AvailabilityStatus
from datetime import datetime


def add_comprehensive_sample_data():
    """Add comprehensive sample pharmaceutical data."""
    
    db = DatabaseManager()
    
    # Comprehensive sample data
    sample_drugs = [
        # Paracetamol variants
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Paracetamol',
            'brand_name': 'Crocin',
            'manufacturer': 'GSK',
            'composition': 'Paracetamol 500mg',
            'dosage_form': 'Tablet',
            'selling_price': 25.0,
            'mrp': 30.0,
            'therapeutic_class': 'Analgesics',
            'uses_indications': ['Pain relief', 'Fever reduction'],
            'pack_size': '10 tablets'
        },
        {
            'source_website': SourceWebsite.APOLLO,
            'drug_name': 'Paracetamol',
            'brand_name': 'Dolo 650',
            'manufacturer': 'Micro Labs',
            'composition': 'Paracetamol 650mg',
            'dosage_form': 'Tablet',
            'selling_price': 22.0,
            'mrp': 28.0,
            'therapeutic_class': 'Analgesics',
            'uses_indications': ['Pain relief', 'Fever reduction'],
            'pack_size': '15 tablets'
        },
        {
            'source_website': SourceWebsite.INDIAMART,
            'drug_name': 'Paracetamol',
            'brand_name': 'Paracip',
            'manufacturer': 'Cipla',
            'composition': 'Paracetamol 500mg',
            'dosage_form': 'Tablet',
            'selling_price': 18.0,
            'mrp': 25.0,
            'therapeutic_class': 'Analgesics',
            'pack_size': '10 tablets'
        },
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Paracetamol',
            'brand_name': 'Calpol',
            'manufacturer': 'GSK',
            'composition': 'Paracetamol 250mg',
            'dosage_form': 'Syrup',
            'selling_price': 45.0,
            'mrp': 55.0,
            'therapeutic_class': 'Analgesics',
            'pack_size': '60ml'
        },
        
        # Aspirin variants
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Aspirin',
            'brand_name': 'Disprin',
            'manufacturer': 'Reckitt Benckiser',
            'composition': 'Aspirin 325mg',
            'dosage_form': 'Tablet',
            'selling_price': 15.0,
            'mrp': 20.0,
            'therapeutic_class': 'Analgesics',
            'uses_indications': ['Pain relief', 'Anti-inflammatory'],
            'pack_size': '10 tablets'
        },
        {
            'source_website': SourceWebsite.APOLLO,
            'drug_name': 'Aspirin',
            'brand_name': 'Ecosprin',
            'manufacturer': 'USV',
            'composition': 'Aspirin 75mg',
            'dosage_form': 'Tablet',
            'selling_price': 12.0,
            'mrp': 18.0,
            'therapeutic_class': 'Analgesics',
            'pack_size': '14 tablets'
        },
        
        # Ibuprofen
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Ibuprofen',
            'brand_name': 'Brufen',
            'manufacturer': 'Abbott',
            'composition': 'Ibuprofen 400mg',
            'dosage_form': 'Tablet',
            'selling_price': 35.0,
            'mrp': 45.0,
            'therapeutic_class': 'Analgesics',
            'uses_indications': ['Pain relief', 'Anti-inflammatory'],
            'pack_size': '10 tablets'
        },
        {
            'source_website': SourceWebsite.APOLLO,
            'drug_name': 'Ibuprofen',
            'brand_name': 'Combiflam',
            'manufacturer': 'Sanofi',
            'composition': 'Ibuprofen 400mg + Paracetamol 325mg',
            'dosage_form': 'Tablet',
            'selling_price': 28.0,
            'mrp': 35.0,
            'therapeutic_class': 'Analgesics',
            'pack_size': '10 tablets'
        },
        
        # Vitamins
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Vitamin D',
            'brand_name': 'Calcirol',
            'manufacturer': 'Cadila',
            'composition': 'Cholecalciferol 60000 IU',
            'dosage_form': 'Capsule',
            'selling_price': 150.0,
            'mrp': 200.0,
            'therapeutic_class': 'Vitamins',
            'uses_indications': ['Vitamin D deficiency', 'Bone health'],
            'pack_size': '4 capsules'
        },
        {
            'source_website': SourceWebsite.APOLLO,
            'drug_name': 'Vitamin D',
            'brand_name': 'D-Rise',
            'manufacturer': 'Alkem',
            'composition': 'Cholecalciferol 1000 IU',
            'dosage_form': 'Tablet',
            'selling_price': 85.0,
            'mrp': 110.0,
            'therapeutic_class': 'Vitamins',
            'pack_size': '30 tablets'
        },
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Vitamin C',
            'brand_name': 'Limcee',
            'manufacturer': 'Abbott',
            'composition': 'Vitamin C 500mg',
            'dosage_form': 'Tablet',
            'selling_price': 45.0,
            'mrp': 55.0,
            'therapeutic_class': 'Vitamins',
            'uses_indications': ['Vitamin C deficiency', 'Immunity'],
            'pack_size': '15 tablets'
        },
        
        # Antibiotics
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Amoxicillin',
            'brand_name': 'Novamox',
            'manufacturer': 'Cipla',
            'composition': 'Amoxicillin 500mg',
            'dosage_form': 'Capsule',
            'selling_price': 85.0,
            'mrp': 100.0,
            'therapeutic_class': 'Antibiotics',
            'uses_indications': ['Bacterial infections'],
            'pack_size': '10 capsules'
        },
        {
            'source_website': SourceWebsite.APOLLO,
            'drug_name': 'Azithromycin',
            'brand_name': 'Azee',
            'manufacturer': 'Cipla',
            'composition': 'Azithromycin 500mg',
            'dosage_form': 'Tablet',
            'selling_price': 125.0,
            'mrp': 150.0,
            'therapeutic_class': 'Antibiotics',
            'pack_size': '3 tablets'
        },
        
        # Antacids
        {
            'source_website': SourceWebsite.ONEMG,
            'drug_name': 'Omeprazole',
            'brand_name': 'Omez',
            'manufacturer': 'Dr. Reddy\'s',
            'composition': 'Omeprazole 20mg',
            'dosage_form': 'Capsule',
            'selling_price': 65.0,
            'mrp': 80.0,
            'therapeutic_class': 'Antacids',
            'uses_indications': ['Acidity', 'GERD'],
            'pack_size': '10 capsules'
        },
        {
            'source_website': SourceWebsite.APOLLO,
            'drug_name': 'Pantoprazole',
            'brand_name': 'Pan',
            'manufacturer': 'Alkem',
            'composition': 'Pantoprazole 40mg',
            'dosage_form': 'Tablet',
            'selling_price': 55.0,
            'mrp': 70.0,
            'therapeutic_class': 'Antacids',
            'pack_size': '10 tablets'
        }
    ]
    
    # Convert to DrugInformation objects and insert
    drugs_added = 0
    for drug_data in sample_drugs:
        drug = DrugInformation(
            source_website=drug_data['source_website'],
            drug_name=drug_data['drug_name'],
            brand_name=drug_data.get('brand_name'),
            manufacturer=drug_data.get('manufacturer'),
            composition=drug_data.get('composition'),
            dosage_form=drug_data.get('dosage_form'),
            selling_price=drug_data.get('selling_price'),
            mrp=drug_data.get('mrp'),
            availability_status=AvailabilityStatus.IN_STOCK,
            therapeutic_class=drug_data.get('therapeutic_class'),
            uses_indications=drug_data.get('uses_indications', []),
            pack_size=drug_data.get('pack_size')
        )
        
        try:
            db.insert_drug(drug)
            drugs_added += 1
            print(f"✅ Added: {drug.drug_name} ({drug.brand_name}) - ₹{drug.selling_price}")
        except Exception as e:
            print(f"❌ Failed to add {drug.drug_name}: {e}")
    
    print(f"\n🎉 Successfully added {drugs_added} drugs to the database!")
    
    # Show statistics
    stats = db.get_database_stats()
    print(f"\n📊 Database Statistics:")
    print(f"   Total drugs: {stats['total_drugs']}")
    print(f"   Sources: {list(stats['by_source'].keys())}")
    print(f"   Therapeutic classes: {list(stats['by_therapeutic_class'].keys())}")
    
    # Test search
    print(f"\n🔍 Testing search for 'paracetamol':")
    results = db.search_drugs(query='paracetamol')
    print(f"   Found {len(results)} results")
    for result in results[:3]:
        print(f"   - {result['drug_name']} ({result['brand_name']}) - ₹{result['selling_price']}")
    
    return drugs_added


if __name__ == '__main__':
    print("🏥 Adding comprehensive sample pharmaceutical data...")
    print("=" * 60)
    
    try:
        count = add_comprehensive_sample_data()
        
        print("\n" + "=" * 60)
        print("✅ SAMPLE DATA ADDED SUCCESSFULLY!")
        print("=" * 60)
        print("\n🚀 Now you can:")
        print("   1. Start the chatbot: python app.py")
        print("   2. Open browser: http://localhost:5000")
        print("   3. Try: 'What's the cheapest paracetamol?'")
        print("\n💡 Example queries:")
        print("   • 'Compare paracetamol prices'")
        print("   • 'Show me vitamins under 100'")
        print("   • 'Tell me about aspirin'")
        print("   • 'Antibiotics available'")
        
    except Exception as e:
        print(f"\n❌ Error adding sample data: {e}")
        print("\nTry running this in your activated virtual environment:")
        print("   venv\\Scripts\\activate.bat")
        print("   python add_sample_data.py")
