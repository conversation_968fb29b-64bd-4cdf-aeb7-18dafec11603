#!/usr/bin/env python3
"""
Example usage of the Pharmaceutical Data Scraper.
Demonstrates various ways to use the scraper programmatically.
"""

import logging
from datetime import datetime
from src.main import PharmaceuticalScraper

def example_basic_scraping():
    """Basic example of scraping pharmaceutical data."""
    print("="*60)
    print("EXAMPLE 1: Basic Scraping")
    print("="*60)
    
    # Initialize scraper
    scraper = PharmaceuticalScraper('config.yaml')
    
    # Define search queries
    queries = ['paracetamol', 'aspirin']
    
    print(f"Searching for: {', '.join(queries)}")
    print("Sources: All available (1mg, Apollo, IndiaMART)")
    
    # Scrape data
    drugs = scraper.scrape_drugs(
        queries=queries,
        sources=['1mg', 'apollo'],  # Limiting to 2 sources for demo
        limit_per_query=10  # Small limit for demo
    )
    
    print(f"\nScraped {len(drugs)} drug records")
    
    # Display sample data
    if drugs:
        print("\nSample record:")
        sample_drug = drugs[0]
        print(f"  Drug: {sample_drug.drug_name}")
        print(f"  Source: {sample_drug.source_website.value}")
        print(f"  Manufacturer: {sample_drug.manufacturer}")
        print(f"  Price: {sample_drug.selling_price} {sample_drug.currency}")
        print(f"  Composition: {sample_drug.composition}")
    
    # Export to Excel
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = scraper.export_data(drugs, f'example_basic_{timestamp}.xlsx')
    print(f"\nData exported to: {output_file}")
    
    return drugs, scraper

def example_targeted_scraping():
    """Example of targeted scraping with specific parameters."""
    print("\n" + "="*60)
    print("EXAMPLE 2: Targeted Scraping")
    print("="*60)
    
    # Initialize scraper
    scraper = PharmaceuticalScraper('config.yaml')
    
    # Search for specific drug categories
    queries = ['vitamin d', 'calcium tablets', 'iron supplements']
    
    print(f"Searching for supplements: {', '.join(queries)}")
    print("Source: 1mg only")
    
    # Scrape data from specific source
    drugs = scraper.scrape_drugs(
        queries=queries,
        sources=['1mg'],
        limit_per_query=15
    )
    
    print(f"\nScraped {len(drugs)} supplement records")
    
    # Analyze data
    if drugs:
        print("\nData Analysis:")
        
        # Price analysis
        prices = [drug.selling_price for drug in drugs if drug.selling_price]
        if prices:
            print(f"  Price range: ₹{min(prices):.2f} - ₹{max(prices):.2f}")
            print(f"  Average price: ₹{sum(prices)/len(prices):.2f}")
        
        # Manufacturer analysis
        manufacturers = [drug.manufacturer for drug in drugs if drug.manufacturer]
        unique_manufacturers = set(manufacturers)
        print(f"  Unique manufacturers: {len(unique_manufacturers)}")
        
        # Dosage form analysis
        dosage_forms = [drug.dosage_form for drug in drugs if drug.dosage_form]
        unique_forms = set(dosage_forms)
        print(f"  Dosage forms found: {', '.join(unique_forms) if unique_forms else 'None'}")
    
    # Export with custom filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = scraper.export_data(drugs, f'supplements_data_{timestamp}.xlsx')
    print(f"\nData exported to: {output_file}")
    
    return drugs, scraper

def example_comprehensive_analysis():
    """Example of comprehensive data analysis."""
    print("\n" + "="*60)
    print("EXAMPLE 3: Comprehensive Analysis")
    print("="*60)
    
    # Initialize scraper
    scraper = PharmaceuticalScraper('config.yaml')
    
    # Search for common medications
    queries = ['paracetamol', 'ibuprofen']
    
    print(f"Comprehensive analysis for: {', '.join(queries)}")
    print("Sources: All available")
    
    # Scrape from all sources
    drugs = scraper.scrape_drugs(
        queries=queries,
        sources=['1mg', 'apollo'],  # Using 2 sources for demo
        limit_per_query=20
    )
    
    print(f"\nScraped {len(drugs)} records for analysis")
    
    if drugs:
        # Comprehensive analysis
        print("\n" + "-"*40)
        print("COMPREHENSIVE ANALYSIS")
        print("-"*40)
        
        # Source distribution
        source_counts = {}
        for drug in drugs:
            source = drug.source_website.value
            source_counts[source] = source_counts.get(source, 0) + 1
        
        print("Records by source:")
        for source, count in source_counts.items():
            print(f"  {source}: {count}")
        
        # Data completeness
        print("\nData completeness:")
        total = len(drugs)
        completeness = {
            'Drug Name': sum(1 for d in drugs if d.drug_name),
            'Manufacturer': sum(1 for d in drugs if d.manufacturer),
            'Composition': sum(1 for d in drugs if d.composition),
            'Price': sum(1 for d in drugs if d.selling_price),
            'Dosage Form': sum(1 for d in drugs if d.dosage_form),
        }
        
        for field, count in completeness.items():
            percentage = (count / total) * 100
            print(f"  {field}: {count}/{total} ({percentage:.1f}%)")
        
        # Price comparison across sources
        print("\nPrice analysis by source:")
        for source in source_counts.keys():
            source_drugs = [d for d in drugs if d.source_website.value == source]
            prices = [d.selling_price for d in source_drugs if d.selling_price]
            
            if prices:
                avg_price = sum(prices) / len(prices)
                print(f"  {source}: Avg ₹{avg_price:.2f} ({len(prices)} records)")
    
    # Export comprehensive report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = scraper.export_data(drugs, f'comprehensive_analysis_{timestamp}.xlsx')
    print(f"\nComprehensive report exported to: {output_file}")
    
    # Get scraping statistics
    stats = scraper.get_scraping_stats()
    print(f"\nSession ID: {stats['session_id']}")
    print("Scraper performance:")
    for source, scraper_stats in stats['scraper_stats'].items():
        if scraper_stats['successful_requests'] > 0:
            print(f"  {source}: {scraper_stats['successful_requests']} successful requests")
    
    return drugs, scraper

def main():
    """Run all examples."""
    print("Pharmaceutical Data Scraper - Example Usage")
    print("=" * 60)
    
    # Set up logging for examples
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Run examples
        example_basic_scraping()
        example_targeted_scraping()
        example_comprehensive_analysis()
        
        print("\n" + "="*60)
        print("ALL EXAMPLES COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\nCheck the generated Excel files for detailed results.")
        print("Each file contains multiple sheets with data, metadata, and statistics.")
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user.")
    except Exception as e:
        print(f"\nError running examples: {str(e)}")
        logging.error(f"Example error: {str(e)}", exc_info=True)

if __name__ == '__main__':
    main()
