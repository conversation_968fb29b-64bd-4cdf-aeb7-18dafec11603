"""
Main application for pharmaceutical data scraping.
Provides CLI interface and orchestrates the scraping process.
"""

import argparse
import logging
import yaml
import uuid
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path
from tqdm import tqdm

from .models.drug_data import DrugInformation, ScrapingMetadata, SourceWebsite
from .scrapers.onemg_scraper import OneMgScraper
from .scrapers.apollo_scraper import ApolloScraper
from .scrapers.indiamart_scraper import IndiaMartScraper
from .utils.data_processor import DataProcessor
from .utils.excel_exporter import ExcelExporter


class PharmaceuticalScraper:
    """
    Main application class for pharmaceutical data scraping.
    """
    
    def __init__(self, config_path: str = 'config.yaml'):
        """
        Initialize the pharmaceutical scraper.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        
        # Initialize components
        self.data_processor = DataProcessor()
        self.excel_exporter = ExcelExporter(self.config)
        
        # Initialize scrapers
        self.scrapers = {
            SourceWebsite.ONEMG: OneMgScraper(self.config),
            SourceWebsite.APOLLO: ApolloScraper(self.config),
            SourceWebsite.INDIAMART: IndiaMartScraper(self.config)
        }
        
        # Session metadata
        self.session_id = str(uuid.uuid4())
        self.metadata = ScrapingMetadata(
            session_id=self.session_id,
            start_time=datetime.now()
        )
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Configuration file {config_path} not found. Using default settings.")
            return self._get_default_config()
        except yaml.YAMLError as e:
            print(f"Error parsing configuration file: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            'scraping': {
                'delay_between_requests': 2,
                'max_retries': 3,
                'timeout': 30,
                'concurrent_requests': 1
            },
            'websites': {
                'onemg': {
                    'base_url': 'https://www.1mg.com',
                    'search_url': 'https://www.1mg.com/search/all',
                    'rate_limit': 2
                },
                'apollo': {
                    'base_url': 'https://www.apollopharmacy.in',
                    'search_url': 'https://www.apollopharmacy.in/search-medicines',
                    'rate_limit': 2
                },
                'indiamart': {
                    'base_url': 'https://www.indiamart.com',
                    'search_url': 'https://www.indiamart.com/search.mp',
                    'rate_limit': 3
                }
            },
            'output': {
                'excel_filename': 'pharmaceutical_data_{timestamp}.xlsx',
                'include_metadata': True,
                'max_records_per_sheet': 1000000
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'scraper.log'
            }
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logging_config = self.config.get('logging', {})
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, logging_config.get('level', 'INFO')),
            format=logging_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(logging_config.get('file', 'scraper.log'))
            ]
        )
        
        return logging.getLogger(__name__)
    
    def scrape_drugs(self, 
                    queries: List[str], 
                    sources: List[str] = None, 
                    limit_per_query: int = 50) -> List[DrugInformation]:
        """
        Scrape drug information from specified sources.
        
        Args:
            queries: List of search queries
            sources: List of source websites to scrape (default: all)
            limit_per_query: Maximum results per query per source
            
        Returns:
            List of DrugInformation objects
        """
        if sources is None:
            sources = ['1mg', 'apollo', 'indiamart']
        
        # Map source names to enums
        source_mapping = {
            '1mg': SourceWebsite.ONEMG,
            'onemg': SourceWebsite.ONEMG,
            'apollo': SourceWebsite.APOLLO,
            'indiamart': SourceWebsite.INDIAMART
        }
        
        selected_sources = []
        for source in sources:
            if source.lower() in source_mapping:
                selected_sources.append(source_mapping[source.lower()])
            else:
                self.logger.warning(f"Unknown source: {source}")
        
        all_drugs = []
        
        self.logger.info(f"Starting scraping session: {self.session_id}")
        self.logger.info(f"Queries: {queries}")
        self.logger.info(f"Sources: {[s.value for s in selected_sources]}")
        
        # Store scraping parameters
        self.metadata.scraping_parameters = {
            'queries': queries,
            'sources': [s.value for s in selected_sources],
            'limit_per_query': limit_per_query
        }
        
        for source in selected_sources:
            scraper = self.scrapers[source]
            source_drugs = []
            
            self.logger.info(f"Scraping from {source.value}...")
            
            for query in queries:
                try:
                    # Search for products
                    self.logger.info(f"Searching for '{query}' on {source.value}")
                    product_urls = scraper.search_drugs(query, limit_per_query)
                    
                    if not product_urls:
                        self.logger.warning(f"No products found for '{query}' on {source.value}")
                        continue
                    
                    # Scrape product details
                    with tqdm(total=len(product_urls), 
                             desc=f"Scraping {source.value} - {query}") as pbar:
                        
                        for url in product_urls:
                            drug_info = scraper.scrape_drug_details(url)
                            if drug_info:
                                # Process and standardize data
                                processed_drug = self.data_processor.process_drug_information(drug_info)
                                source_drugs.append(processed_drug)
                            
                            pbar.update(1)
                    
                except Exception as e:
                    error_msg = f"Error scraping '{query}' from {source.value}: {str(e)}"
                    self.logger.error(error_msg)
                    self.metadata.errors_encountered.append(error_msg)
            
            # Update metadata
            self.metadata.records_by_source[source.value] = len(source_drugs)
            all_drugs.extend(source_drugs)
            
            self.logger.info(f"Scraped {len(source_drugs)} records from {source.value}")
        
        # Deduplicate drugs
        self.logger.info("Removing duplicates...")
        all_drugs = self.data_processor.deduplicate_drugs(all_drugs)
        
        # Update final metadata
        self.metadata.end_time = datetime.now()
        self.metadata.total_records_scraped = len(all_drugs)
        
        self.logger.info(f"Scraping completed. Total records: {len(all_drugs)}")
        
        return all_drugs
    
    def export_data(self, drugs: List[DrugInformation], filename: str = None) -> str:
        """
        Export scraped data to Excel file.
        
        Args:
            drugs: List of DrugInformation objects
            filename: Output filename (optional)
            
        Returns:
            Path to exported file
        """
        return self.excel_exporter.export_to_excel(drugs, self.metadata, filename)
    
    def get_scraping_stats(self) -> Dict[str, Any]:
        """Get comprehensive scraping statistics."""
        stats = {
            'session_id': self.session_id,
            'metadata': self.metadata.to_dict(),
            'scraper_stats': {}
        }
        
        for source, scraper in self.scrapers.items():
            stats['scraper_stats'][source.value] = scraper.get_stats()
        
        return stats


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='Pharmaceutical Data Scraper')
    
    parser.add_argument('queries', nargs='+', help='Search queries for drugs')
    parser.add_argument('--sources', nargs='+', 
                       choices=['1mg', 'apollo', 'indiamart'], 
                       default=['1mg', 'apollo', 'indiamart'],
                       help='Sources to scrape from')
    parser.add_argument('--limit', type=int, default=50,
                       help='Maximum results per query per source')
    parser.add_argument('--output', '-o', type=str,
                       help='Output Excel filename')
    parser.add_argument('--config', '-c', type=str, default='config.yaml',
                       help='Configuration file path')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Initialize scraper
    scraper = PharmaceuticalScraper(args.config)
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Scrape data
        drugs = scraper.scrape_drugs(
            queries=args.queries,
            sources=args.sources,
            limit_per_query=args.limit
        )
        
        if not drugs:
            print("No data scraped. Please check your queries and try again.")
            return
        
        # Export data
        output_file = scraper.export_data(drugs, args.output)
        
        # Print summary
        stats = scraper.get_scraping_stats()
        print(f"\n{'='*50}")
        print("SCRAPING SUMMARY")
        print(f"{'='*50}")
        print(f"Total records scraped: {len(drugs)}")
        print(f"Output file: {output_file}")
        print(f"Session ID: {scraper.session_id}")
        
        print("\nRecords by source:")
        for source, count in stats['metadata']['records_by_source'].items():
            print(f"  {source}: {count}")
        
        if stats['metadata']['errors_count'] > 0:
            print(f"\nErrors encountered: {stats['metadata']['errors_count']}")
        
        print(f"\nScraping completed successfully!")
        
    except KeyboardInterrupt:
        print("\nScraping interrupted by user.")
    except Exception as e:
        print(f"Error during scraping: {str(e)}")
        logging.getLogger().error(f"Fatal error: {str(e)}", exc_info=True)


if __name__ == '__main__':
    main()
