# Pharmaceutical Data Scraper Configuration

# Scraping settings
scraping:
  delay_between_requests: 2  # seconds
  max_retries: 3
  timeout: 30  # seconds
  concurrent_requests: 1  # Keep low to be respectful
  
# User agents for rotation
user_agents:
  - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# Website-specific configurations
websites:
  onemg:
    base_url: "https://www.1mg.com"
    search_url: "https://www.1mg.com/search/all"
    rate_limit: 2  # seconds between requests
    
  apollo:
    base_url: "https://www.apollopharmacy.in"
    search_url: "https://www.apollopharmacy.in/search-medicines"
    rate_limit: 2
    
  indiamart:
    base_url: "https://www.indiamart.com"
    search_url: "https://www.indiamart.com/search.mp"
    rate_limit: 3  # More conservative for IndiaMART

# Output settings
output:
  excel_filename: "pharmaceutical_data_{timestamp}.xlsx"
  include_metadata: true
  max_records_per_sheet: 1000000  # Excel limit
  
# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "scraper.log"
