"""
Base scraper class with common functionality for pharmaceutical websites.
Provides rate limiting, error handling, logging, and user agent rotation.
"""

import time
import random
import logging
import requests
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from fake_useragent import UserAgent
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..models.drug_data import DrugInformation, SourceWebsite


class BaseScraper(ABC):
    """
    Abstract base class for pharmaceutical website scrapers.
    
    Provides common functionality including:
    - Rate limiting and respectful scraping
    - Error handling and retries
    - User agent rotation
    - Session management
    - Logging
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the base scraper.
        
        Args:
            config: Configuration dictionary containing scraping parameters
        """
        self.config = config
        self.logger = self._setup_logging()
        self.session = self._setup_session()
        self.user_agent = UserAgent()
        
        # Rate limiting
        self.delay_between_requests = config.get('scraping', {}).get('delay_between_requests', 2)
        self.last_request_time = 0
        
        # Error tracking
        self.errors = []
        self.successful_requests = 0
        self.failed_requests = 0
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging for the scraper."""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(getattr(logging, self.config.get('logging', {}).get('level', 'INFO')))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                self.config.get('logging', {}).get('format', 
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_session(self) -> requests.Session:
        """Set up requests session with retry strategy."""
        session = requests.Session()
        
        # Retry strategy
        retry_strategy = Retry(
            total=self.config.get('scraping', {}).get('max_retries', 3),
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers with rotated user agent."""
        return {
            'User-Agent': self.user_agent.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def _respect_rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.delay_between_requests:
            sleep_time = self.delay_between_requests - time_since_last_request
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[requests.Response]:
        """
        Make a request with rate limiting and error handling.
        
        Args:
            url: URL to request
            method: HTTP method (GET, POST, etc.)
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object or None if request failed
        """
        self._respect_rate_limit()
        
        try:
            headers = kwargs.pop('headers', {})
            headers.update(self._get_headers())
            
            timeout = kwargs.pop('timeout', self.config.get('scraping', {}).get('timeout', 30))
            
            self.logger.debug(f"Making {method} request to: {url}")
            
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                timeout=timeout,
                **kwargs
            )
            
            response.raise_for_status()
            self.successful_requests += 1
            
            return response
            
        except requests.exceptions.RequestException as e:
            self.failed_requests += 1
            error_msg = f"Request failed for {url}: {str(e)}"
            self.logger.error(error_msg)
            self.errors.append(error_msg)
            return None
    
    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and belongs to the expected domain."""
        try:
            parsed = urlparse(url)
            return bool(parsed.netloc and parsed.scheme)
        except Exception:
            return False
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text data."""
        if not text:
            return None
        
        # Remove extra whitespace and normalize
        cleaned = ' '.join(text.strip().split())
        
        # Remove common unwanted characters
        cleaned = cleaned.replace('\n', ' ').replace('\t', ' ').replace('\r', ' ')
        
        return cleaned if cleaned else None
    
    def extract_price(self, price_text: str) -> Optional[float]:
        """Extract numeric price from text."""
        if not price_text:
            return None
        
        try:
            # Remove currency symbols and extract numbers
            import re
            price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
            if price_match:
                return float(price_match.group())
        except (ValueError, AttributeError):
            pass
        
        return None
    
    @abstractmethod
    def get_source_website(self) -> SourceWebsite:
        """Return the source website enum for this scraper."""
        pass
    
    @abstractmethod
    def search_drugs(self, query: str, limit: int = 50) -> List[str]:
        """
        Search for drugs and return list of product URLs.
        
        Args:
            query: Search query
            limit: Maximum number of results to return
            
        Returns:
            List of product URLs
        """
        pass
    
    @abstractmethod
    def scrape_drug_details(self, product_url: str) -> Optional[DrugInformation]:
        """
        Scrape detailed drug information from a product page.
        
        Args:
            product_url: URL of the product page
            
        Returns:
            DrugInformation object or None if scraping failed
        """
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get scraping statistics."""
        return {
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'total_errors': len(self.errors),
            'error_rate': self.failed_requests / max(1, self.successful_requests + self.failed_requests)
        }
