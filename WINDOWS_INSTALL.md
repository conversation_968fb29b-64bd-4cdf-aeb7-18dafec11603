# 🪟 Windows Installation Guide - Pharmaceutical Chatbot

## 🚨 **Fixing the Path Issue**

### **Problem**: Windows paths with spaces cause installation failures
### **Solution**: Choose one of these methods:

---

## 🔧 **Method 1: Rename Folder (Recommended)**

Open **Command Prompt** and run:

```cmd
cd "C:\Users\<USER>\OneDrive\Documents"
ren "medical data esxtraction pipeline" "medical-data-extraction-pipeline"
cd "medical-data-extraction-pipeline"
```

✅ **This permanently fixes the issue**

---

## 🔧 **Method 2: Use Windows Batch Installer**

**Double-click** `install_windows.bat` or run in Command Prompt:

```cmd
cd /d "C:\Users\<USER>\OneDrive\Documents\medical data esxtraction pipeline"
install_windows.bat
```

✅ **Handles paths automatically**

---

## 🔧 **Method 3: Manual Installation (Foolproof)**

### **Step 1: Open Command Prompt in Project Folder**
1. Open File Explorer
2. Navigate to: `C:\Users\<USER>\OneDrive\Documents\medical data esxtraction pipeline`
3. Type `cmd` in the address bar and press Enter
4. Command Prompt opens in the correct folder

### **Step 2: Check Python**
```cmd
python --version
```
Should show Python 3.7+

### **Step 3: Create Virtual Environment**
```cmd
python -m venv venv
```

### **Step 4: Activate Virtual Environment**
```cmd
venv\Scripts\activate.bat
```
You should see `(venv)` in your prompt

### **Step 5: Upgrade Pip**
```cmd
python -m pip install --upgrade pip
```

### **Step 6: Install Dependencies**
```cmd
pip install -r requirements.txt
```

**If this fails, try individual packages:**
```cmd
pip install flask flask-cors sqlalchemy pandas openpyxl
pip install beautifulsoup4 requests fuzzywuzzy python-levenshtein
pip install nltk pyyaml tqdm fake-useragent
```

### **Step 7: Test Installation**
```cmd
python -c "import flask, sqlite3; print('Installation OK')"
```

### **Step 8: Initialize Database**
```cmd
python -c "from src.database.db_manager import DatabaseManager; DatabaseManager(); print('Database ready')"
```

### **Step 9: Collect Sample Data**
```cmd
python collect_data.py --category pain_relief --limit 10
```

### **Step 10: Start the Chatbot**
```cmd
python app.py
```

---

## 🌐 **Access the Chatbot**

1. **Open your browser**
2. **Go to**: http://localhost:5000
3. **Try these queries**:
   - "What's the cheapest paracetamol?"
   - "Compare aspirin prices"
   - "Show me vitamins under 100"

---

## 🚨 **Troubleshooting**

### **Error: "Module not found"**
```cmd
# Reinstall in virtual environment
venv\Scripts\activate.bat
pip install -r requirements.txt
```

### **Error: "Database not found"**
```cmd
# Collect some data first
python collect_data.py --category pain_relief --limit 5
```

### **Error: "Port 5000 in use"**
```cmd
# Use different port
set FLASK_PORT=8080
python app.py
```

### **Error: "Permission denied"**
```cmd
# Run Command Prompt as Administrator
# Right-click Command Prompt → "Run as administrator"
```

### **Error: "Path not found"**
```cmd
# Use quotes around paths
cd /d "C:\Users\<USER>\OneDrive\Documents\medical data esxtraction pipeline"
```

---

## 🎯 **Quick Test Commands**

### **Test Database**
```cmd
python -c "from src.database.db_manager import DatabaseManager; print(DatabaseManager().get_database_stats())"
```

### **Test Chatbot**
```cmd
python -c "from src.chatbot.intelligence import PharmaChatbot; print(PharmaChatbot().process_query('help'))"
```

### **Test Web Server**
```cmd
python app.py
# Should show: "Running on http://127.0.0.1:5000"
```

---

## 📊 **Data Collection Options**

### **Quick Start (5 minutes)**
```cmd
python collect_data.py --category pain_relief --limit 10
```

### **More Categories (15 minutes)**
```cmd
python collect_data.py --category pain_relief --limit 15
python collect_data.py --category vitamins --limit 15
python collect_data.py --category antibiotics --limit 10
```

### **Full Collection (30-60 minutes)**
```cmd
python collect_data.py --limit 20
```

### **Check Collection Status**
```cmd
python collect_data.py --report
```

---

## 🎮 **Demo Mode**

Test everything works:
```cmd
python demo_chatbot.py
```

This will:
- ✅ Show database statistics
- ✅ Demonstrate all chatbot features
- ✅ Let you try interactive queries
- ✅ Verify everything is working

---

## 🚀 **Startup Scripts**

### **Create Convenient Startup**
```cmd
echo @echo off > start.bat
echo cd /d "%~dp0" >> start.bat
echo call venv\Scripts\activate.bat >> start.bat
echo python app.py >> start.bat
echo pause >> start.bat
```

### **Use the Startup Script**
Double-click `start.bat` to launch the chatbot

---

## 🔧 **Advanced Configuration**

### **Change Port**
Edit `config.yaml`:
```yaml
web:
  port: 8080  # Instead of 5000
```

### **Faster Data Collection**
Edit `config.yaml`:
```yaml
scraping:
  delay_between_requests: 1  # Faster, but be respectful
```

### **Different Database Location**
```cmd
set DATABASE_PATH=C:\MyData\drugs.db
python app.py
```

---

## 📱 **Mobile Access**

1. **Find your computer's IP**:
   ```cmd
   ipconfig | findstr IPv4
   ```

2. **Access from phone**: `http://YOUR_IP:5000`

3. **Make sure Windows Firewall allows it**

---

## 🆘 **Still Having Issues?**

### **Complete Reset**
```cmd
# Delete virtual environment
rmdir /s venv

# Delete database
del pharmaceutical_data.db

# Start fresh
python -m venv venv
venv\Scripts\activate.bat
pip install flask sqlalchemy pandas beautifulsoup4 requests
python collect_data.py --category pain_relief --limit 5
python app.py
```

### **Minimal Installation**
If all else fails, install just the essentials:
```cmd
pip install flask sqlalchemy pandas
python -c "from src.database.db_manager import DatabaseManager; DatabaseManager()"
python app.py
```

Then manually add some test data through the web interface.

---

## ✅ **Success Checklist**

- [ ] Python 3.7+ installed
- [ ] Virtual environment created and activated
- [ ] Dependencies installed without errors
- [ ] Database initialized successfully
- [ ] Sample data collected (at least 10 drugs)
- [ ] Web server starts without errors
- [ ] Browser shows chatbot interface at http://localhost:5000
- [ ] Can send test query and get response

---

## 🎉 **You're Ready!**

Once everything is working:

1. **Bookmark**: http://localhost:5000
2. **Try queries**: "cheapest paracetamol", "compare aspirin"
3. **Collect more data**: Run collection scripts for more categories
4. **Share**: Access from other devices on your network

**Happy chatting! 🤖💊**
