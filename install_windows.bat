@echo off
echo ================================================================
echo   PHARMACEUTICAL CHATBOT - WINDOWS INSTALLER
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
python --version

REM Create virtual environment
echo.
echo [1] Creating Virtual Environment...
echo ----------------------------------------
if exist "venv" (
    echo ✅ Virtual environment already exists
) else (
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
)

REM Activate virtual environment and install dependencies
echo.
echo [2] Installing Dependencies...
echo ----------------------------------------
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

echo Upgrading pip...
python -m pip install --upgrade pip

echo Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    echo.
    echo Trying alternative installation...
    pip install flask flask-cors sqlalchemy pandas openpyxl beautifulsoup4 requests fuzzywuzzy python-levenshtein nltk pyyaml tqdm fake-useragent
)

REM Test installation
echo.
echo [3] Testing Installation...
echo ----------------------------------------
python -c "import flask, sqlite3; print('✅ Core dependencies OK')"
if errorlevel 1 (
    echo ❌ Installation test failed
    pause
    exit /b 1
)

REM Initialize database
echo.
echo [4] Setting Up Database...
echo ----------------------------------------
python -c "from src.database.db_manager import DatabaseManager; DatabaseManager(); print('✅ Database initialized')"
if errorlevel 1 (
    echo ❌ Database setup failed
    pause
    exit /b 1
)

REM Ask about data collection
echo.
echo [5] Data Collection...
echo ----------------------------------------
echo Would you like to collect sample pharmaceutical data now?
echo This will take 5-10 minutes and requires internet connection.
echo.
set /p collect="Collect data now? (y/n): "
if /i "%collect%"=="y" (
    echo.
    echo Collecting sample data...
    python collect_data.py --category pain_relief --limit 10
    echo.
    echo ✅ Sample data collected
) else (
    echo ⚠️  Skipping data collection
    echo You can run this later: python collect_data.py --category pain_relief --limit 10
)

REM Create startup script
echo.
echo [6] Creating Startup Script...
echo ----------------------------------------
echo @echo off > start_chatbot.bat
echo echo Starting Pharmaceutical Chatbot... >> start_chatbot.bat
echo cd /d "%~dp0" >> start_chatbot.bat
echo call venv\Scripts\activate.bat >> start_chatbot.bat
echo python app.py >> start_chatbot.bat
echo pause >> start_chatbot.bat

echo ✅ Created start_chatbot.bat

REM Final test
echo.
echo [7] Final Test...
echo ----------------------------------------
python -c "from src.chatbot.intelligence import PharmaChatbot; print('✅ Chatbot ready')"
if errorlevel 1 (
    echo ❌ Chatbot test failed
    pause
    exit /b 1
)

echo.
echo ================================================================
echo   INSTALLATION COMPLETED SUCCESSFULLY! 🎉
echo ================================================================
echo.
echo Your Pharmaceutical Chatbot is ready to use!
echo.
echo 🚀 To start the chatbot:
echo   1. Double-click "start_chatbot.bat"
echo   2. OR run: python app.py
echo.
echo 🌐 Then open your browser to: http://localhost:5000
echo.
echo 💡 Example queries to try:
echo   • "What's the cheapest paracetamol?"
echo   • "Compare aspirin prices"
echo   • "Show me vitamins under 100"
echo.
echo 📊 To collect more data:
echo   python collect_data.py --category vitamins --limit 15
echo.
set /p start="Start the chatbot now? (y/n): "
if /i "%start%"=="y" (
    echo.
    echo Starting chatbot...
    python app.py
) else (
    echo.
    echo Installation complete! Run start_chatbot.bat when ready.
)

pause
