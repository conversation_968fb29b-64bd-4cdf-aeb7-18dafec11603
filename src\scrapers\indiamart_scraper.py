"""
IndiaMART scraper for extracting pharmaceutical product information.
"""

import re
from typing import List, Optional
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..models.drug_data import DrugInformation, SourceWebsite, AvailabilityStatus


class IndiaMartScraper(BaseScraper):
    """Scraper for IndiaMART pharmaceutical products."""
    
    def __init__(self, config):
        super().__init__(config)
        self.base_url = self.config.get('websites', {}).get('indiamart', {}).get('base_url', 'https://www.indiamart.com')
        self.search_url = self.config.get('websites', {}).get('indiamart', {}).get('search_url', 'https://www.indiamart.com/search.mp')
        
        # IndiaMART requires more conservative scraping
        self.delay_between_requests = self.config.get('websites', {}).get('indiamart', {}).get('rate_limit', 3)
    
    def get_source_website(self) -> SourceWebsite:
        """Return the source website enum."""
        return SourceWebsite.INDIAMART
    
    def search_drugs(self, query: str, limit: int = 50) -> List[str]:
        """
        Search for pharmaceutical products on IndiaMART and return product URLs.
        
        Args:
            query: Search query for drugs/pharmaceutical products
            limit: Maximum number of results to return
            
        Returns:
            List of product URLs
        """
        product_urls = []
        
        try:
            # Add pharmaceutical context to search
            pharma_query = f"{query} pharmaceutical medicine drug"
            search_params = f"?ss={quote(pharma_query)}"
            search_url = f"{self.search_url}{search_params}"
            
            self.logger.info(f"Searching IndiaMART for: {query}")
            response = self.make_request(search_url)
            
            if not response:
                return product_urls
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find product links - IndiaMART uses specific patterns
            product_links = soup.find_all('a', href=re.compile(r'/proddetail/'))
            
            if not product_links:
                # Fallback: look for product card links
                product_links = soup.find_all('a', class_=re.compile(r'product|item'))
            
            for link in product_links[:limit]:
                href = link.get('href')
                if href:
                    # IndiaMART URLs might be relative or absolute
                    if href.startswith('http'):
                        full_url = href
                    else:
                        full_url = urljoin(self.base_url, href)
                    
                    if self.is_valid_url(full_url):
                        product_urls.append(full_url)
            
            self.logger.info(f"Found {len(product_urls)} product URLs for query: {query}")
            
        except Exception as e:
            self.logger.error(f"Error searching IndiaMART for '{query}': {str(e)}")
            self.errors.append(f"Search error for '{query}': {str(e)}")
        
        return product_urls
    
    def scrape_drug_details(self, product_url: str) -> Optional[DrugInformation]:
        """
        Scrape detailed pharmaceutical product information from an IndiaMART product page.
        
        Args:
            product_url: URL of the product page
            
        Returns:
            DrugInformation object or None if scraping failed
        """
        try:
            self.logger.debug(f"Scraping IndiaMART product: {product_url}")
            response = self.make_request(product_url)
            
            if not response:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Initialize drug information
            drug_info = DrugInformation(
                source_website=self.get_source_website(),
                product_url=product_url
            )
            
            # Extract product name (drug name)
            product_name_elem = soup.find('h1', class_=re.compile(r'prd_name|product.name'))
            if not product_name_elem:
                product_name_elem = soup.find('h1')
            
            if product_name_elem:
                drug_info.drug_name = self.clean_text(product_name_elem.get_text())
            
            # Extract seller information (manufacturer/supplier)
            seller_elem = soup.find('div', class_=re.compile(r'company|seller'))
            if not seller_elem:
                seller_elem = soup.find('a', class_=re.compile(r'company|seller'))
            
            if seller_elem:
                drug_info.seller_name = self.clean_text(seller_elem.get_text())
                # For IndiaMART, seller often acts as manufacturer/distributor
                drug_info.manufacturer = drug_info.seller_name
            
            # Extract seller location
            location_elem = soup.find('span', class_=re.compile(r'location|address'))
            if location_elem:
                drug_info.seller_location = self.clean_text(location_elem.get_text())
            
            # Extract price information
            price_elem = soup.find('span', class_=re.compile(r'price|cost|rate'))
            if not price_elem:
                price_elem = soup.find('div', class_=re.compile(r'price'))
            
            if price_elem:
                price_text = price_elem.get_text()
                drug_info.selling_price = self.extract_price(price_text)
            
            # Extract product specifications from table
            spec_table = soup.find('table', class_=re.compile(r'spec|detail'))
            if spec_table:
                rows = spec_table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = self.clean_text(cells[0].get_text()).lower()
                        value = self.clean_text(cells[1].get_text())
                        
                        if 'composition' in key or 'ingredient' in key:
                            drug_info.composition = value
                        elif 'form' in key or 'type' in key:
                            drug_info.dosage_form = value
                        elif 'strength' in key or 'dosage' in key:
                            drug_info.strength = value
                        elif 'pack' in key or 'quantity' in key:
                            drug_info.pack_size = value
                        elif 'brand' in key:
                            drug_info.brand_name = value
                        elif 'therapeutic' in key or 'class' in key:
                            drug_info.therapeutic_class = value
                        elif 'use' in key or 'indication' in key:
                            drug_info.uses_indications = [value]
            
            # Extract additional details from description
            description_elem = soup.find('div', class_=re.compile(r'description|detail'))
            if description_elem:
                description_text = description_elem.get_text()
                
                # Try to extract composition if not found
                if not drug_info.composition:
                    comp_match = re.search(r'composition[:\s]+([^.]+)', description_text, re.I)
                    if comp_match:
                        drug_info.composition = self.clean_text(comp_match.group(1))
                
                # Try to extract uses if not found
                if not drug_info.uses_indications:
                    uses_match = re.search(r'use[ds]?[:\s]+([^.]+)', description_text, re.I)
                    if uses_match:
                        drug_info.uses_indications = [self.clean_text(uses_match.group(1))]
            
            # Extract minimum order quantity (common in IndiaMART)
            moq_elem = soup.find('span', string=re.compile(r'Minimum Order Quantity', re.I))
            if moq_elem:
                moq_value = moq_elem.find_next()
                if moq_value:
                    drug_info.additional_info['minimum_order_quantity'] = self.clean_text(moq_value.get_text())
            
            # Extract packaging details
            packaging_elem = soup.find('span', string=re.compile(r'Packaging', re.I))
            if packaging_elem:
                packaging_value = packaging_elem.find_next()
                if packaging_value:
                    drug_info.additional_info['packaging'] = self.clean_text(packaging_value.get_text())
            
            # Extract delivery time
            delivery_elem = soup.find('span', string=re.compile(r'Delivery Time', re.I))
            if delivery_elem:
                delivery_value = delivery_elem.find_next()
                if delivery_value:
                    drug_info.additional_info['delivery_time'] = self.clean_text(delivery_value.get_text())
            
            # For IndiaMART, availability is usually assumed to be in stock unless specified
            drug_info.availability_status = AvailabilityStatus.IN_STOCK
            
            # Extract product ID from URL
            product_id_match = re.search(r'/proddetail/([^/]+)', product_url)
            if product_id_match:
                drug_info.product_id = product_id_match.group(1)
            
            # Extract image URLs
            img_elements = soup.find_all('img', src=re.compile(r'product|item'))
            drug_info.image_urls = []
            for img in img_elements:
                src = img.get('src')
                if src:
                    if src.startswith('http'):
                        drug_info.image_urls.append(src)
                    else:
                        drug_info.image_urls.append(urljoin(self.base_url, src))
            
            # Extract seller rating if available
            rating_elem = soup.find('span', class_=re.compile(r'rating|star'))
            if rating_elem:
                rating_text = rating_elem.get_text()
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    try:
                        drug_info.seller_rating = float(rating_match.group(1))
                    except ValueError:
                        pass
            
            # Set drug category as unknown for IndiaMART (B2B platform)
            drug_info.drug_category = "B2B/Wholesale"
            
            self.logger.debug(f"Successfully scraped product: {drug_info.drug_name}")
            return drug_info
            
        except Exception as e:
            self.logger.error(f"Error scraping IndiaMART product {product_url}: {str(e)}")
            self.errors.append(f"Product scraping error for {product_url}: {str(e)}")
            return None
